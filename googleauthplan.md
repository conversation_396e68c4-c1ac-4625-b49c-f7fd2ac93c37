# Google Authentication Implementation Plan

## Project Configuration
```typescript
GOOG<PERSON>_CLIENT_ID=90602971467-p2a9ohurea4sqp15u75ltod41k9a0rci.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
```

## 1. Overview
### Purpose
- Implement Google authentication for child users through widget
- Maintain existing parent-child relationship structure
- Support dual authentication methods (email and Google)

### Key Requirements
- Preserve parent-child relationships using parentPublicKey
- Support Google OAuth flow in widget
- Maintain security and data integrity

## 2. Technical Architecture

### Database Schema Updates
```typescript
interface User {
    // Existing fields
    userID: string;
    email: string;
    accountType: 'parent' | 'child';
    parentAccount: string;
    
    // New fields for Google Auth
    googleId?: string;
    authProvider: 'email' | 'google';
    lastLoginProvider?: 'email' | 'google';
}
```

### Required Indexes
- email + parentAccount (existing)
- googleId + parentAccount (new)

## 3. Authentication Flow

### Widget Implementation
1. User initiates Google sign-in
2. Widget receives Google tokens
3. Backend verifies tokens
4. System creates/updates user record
5. Returns JWT token for authentication

```mermaid
sequenceDiagram
    User->>Widget: Click Google Sign-in
    Widget->>Google: OAuth Request
    Google->>User: Authorization
    Google->>Widget: Return tokens
    Widget->>Backend: Auth Request + parentPublicKey
    Backend->>Google: Verify tokens
    Backend->>DB: Create/Update user
    Backend->>Widget: Return JWT
```

## 4. Implementation Steps

### Step 1: Environment Configuration
```typescript
interface Environment {
    GOOGLE_CLIENT_ID: string;
    GOOGLE_CLIENT_SECRET: string;
    GOOGLE_REDIRECT_URI: string;
}
```

### Step 2: Google Auth Service
```typescript
interface GoogleProfile {
    sub: string;
    email: string;
    email_verified: boolean;
    name: string;
}

class GoogleAuthService {
    async verifyToken(token: string): Promise<GoogleProfile>;
    async createOrUpdateUser(profile: GoogleProfile, parentPublicKey: string): Promise<User>;
}
```

### Step 3: API Endpoints
```typescript
POST /auth/google
Body: {
    idToken: string;
    parentPublicKey: string;
}

Response: {
    token: string;
    user: UserResponse
}
```

## 5. Error Handling

### Error Types
```typescript
enum GoogleAuthError {
    INVALID_TOKEN = 'INVALID_TOKEN',
    INVALID_PARENT = 'INVALID_PARENT',
    EMAIL_NOT_VERIFIED = 'EMAIL_NOT_VERIFIED',
    ACCOUNT_EXISTS = 'ACCOUNT_EXISTS'
}
```

## 6. Security Considerations

### Token Verification
- Validate Google ID tokens
- Verify token audience matches client ID
- Check email verification status

### Data Protection
- Encrypt sensitive data
- Store minimal Google account information
- Implement token rotation

## 7. Testing Strategy

### Unit Tests
- Google token verification
- User creation/update logic
- Parent-child relationship validation

### Integration Tests
- Complete Google auth flow
- Widget integration
- Database operations

### Security Tests
- Token validation
- Authorization checks
- Relationship verification

## 8. Implementation Timeline

### Phase 1: Setup (Week 1)
- Environment configuration
- Database schema updates
- Basic Google OAuth integration

### Phase 2: Core Implementation (Week 2)
- Google auth service
- API endpoints
- Widget integration

### Phase 3: Testing & Security (Week 3)
- Unit tests
- Integration tests
- Security audits

## 9. Monitoring & Maintenance

### Metrics to Track
- Google auth success rate
- Token verification failures
- Error rates by type
- Authentication response times

### Maintenance Tasks
- Regular security updates
- Token rotation
- Performance monitoring
- Error log analysis