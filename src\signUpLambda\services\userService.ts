import { User, CreateUserInput } from '../../shared/types';
import { hashPassword } from '../../shared/utils/passwordUtils';
import { generateVerificationToken } from '../../shared/verification/tokenGenerator';
import { VERIFICATION_TOKEN_EXPIRY } from '../../shared/constants';
import { createUser, getUserByEmail, getUserByUsername, getUserByEmailAndParent } from '../../shared/database/userOperations';
import { ErrorResponse } from '../../shared/responseUtils';

export interface CreateUserServiceInput {
  username: string;
  email: string;
  password: string;
  parentPublicKey?: string;
}

export class UserService {
  public static async createUser(input: CreateUserServiceInput, tableName?: string): Promise<{
    user: User;
    verificationToken: string;
  }> {
    const currentTimestamp = Date.now();
    const verificationToken = generateVerificationToken();
    
    // Hash password first
    const hashedPassword = await hashPassword(input.password);

    // Different validation logic for parent vs child accounts
    if (!input.parentPublicKey) {
      // PARENT ACCOUNT: Must be unique across all accounts
      const [existingEmail, existingUsername] = await Promise.all([
        getUserByEmail(input.email, tableName),
        input.username ? getUserByUsername(input.username, tableName) : Promise.resolve(null)
      ]);

      if (existingEmail) {
        throw ErrorResponse(409, 'EMAIL_ALREADY_EXISTS', 'Email is already registered');
      }
      if (existingUsername) {
        throw ErrorResponse(409, 'USERNAME_ALREADY_EXISTS', 'Username is already taken');
      }
    } else {
      // CHILD ACCOUNT: Only check duplicates under the same parent
      const existingUser = await getUserByEmailAndParent(input.email, input.parentPublicKey, tableName);
      if (existingUser) {
        throw ErrorResponse(409, 'DUPLICATE_EMAIL_USERNAME_COMBO', 
          'A user with the same email or username already exists under this parent account');
      }
    }

    // Create user with single DB operation
    const user = await createUser({
      username: input.username,
      email: input.email,
      password: hashedPassword,
      verificationToken,
      verificationTokenExpiry: currentTimestamp + VERIFICATION_TOKEN_EXPIRY,
      accountType: input.parentPublicKey ? 'child' : 'parent',
      accountStatus: 'active',
      parentAccount: input.parentPublicKey || 'ROOT',
      createdAt: currentTimestamp,
      accountBalance: input.parentPublicKey ? undefined : 3.00,
      availableBalance: input.parentPublicKey ? undefined : 3.00,
      lastPasswordChanged: currentTimestamp
    }, tableName);

    return { user, verificationToken };
  }
}
