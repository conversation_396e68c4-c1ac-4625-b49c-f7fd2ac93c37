import { DynamoDB, Lambda } from 'aws-sdk';
import { APIGatewayProxyEvent } from 'aws-lambda';
import { getConfig } from '../services/configService';
import { loadParametersFromEnv } from './parameterLoader';

const lambda = new Lambda();

interface CostCalculation {
  totalAccounts: number;
  accountsCost: number;
  totalOperations: number;
  ioCost: number;
  totalCost: number;
}

export const calculateCostsForUser = async (publicKey: string, event?: APIGatewayProxyEvent): Promise<CostCalculation> => {
  try {
    console.log('Starting cost calculation for publicKey:', publicKey);
    
    // Get configuration based on whether event is provided
    let config;
    if (event) {
      config = await getConfig(event);
    } else {
      config = await loadParametersFromEnv();
    }
    
    const lambdaName = config.COST_CALCULATOR_LAMBDA_NAME || 'costCalculatorLambda';
    const internalServiceKey = config.INTERNAL_SERVICE_KEY || process.env.INTERNAL_SERVICE_KEY;
    
    console.log('[CONFIG] Using configuration for cost calculation', {
      environment: event ? (event.headers.host?.includes('staging') ? 'staging' : 'live') : process.env.ENVIRONMENT || 'live',
      lambdaName
    });
    
    // When invoking the lambda directly, structure the event properly to match API Gateway format
    const params = {
      FunctionName: lambdaName,
      InvocationType: 'RequestResponse',
      Payload: JSON.stringify({
        headers: {
          'x-internal-service': internalServiceKey,
          'x-public-key': publicKey,
          // Add host header to ensure environment detection works
          'host': process.env.ENVIRONMENT === 'staging' ? 'staging.api.authiqa.com' : 'api.authiqa.com'
        }
      })
    };
    
    console.log('Invoking lambda with params:', JSON.stringify(params, null, 2));
    
    const response = await lambda.invoke(params).promise();
    console.log('Raw Lambda response:', JSON.stringify(response, null, 2));
    
    if (response.FunctionError) {
      console.error('Lambda execution failed:', response.FunctionError);
      throw new Error(`Lambda execution failed: ${response.FunctionError}`);
    }

    const costCalculatorResult = JSON.parse(response.Payload as string);
    console.log('Parsed costCalculatorResult:', JSON.stringify(costCalculatorResult, null, 2));

    if (costCalculatorResult.statusCode !== 200) {
      console.error('Lambda returned non-200 status:', costCalculatorResult);
      throw new Error(`Cost calculation failed: ${costCalculatorResult.body}`);
    }

    const body = JSON.parse(costCalculatorResult.body);
    console.log('Parsed response body:', JSON.stringify(body, null, 2));

    return {
      totalAccounts: body.operationCounts.totalAccounts,
      accountsCost: Number(Number(body.costs.breakdown.childAccounts).toFixed(2)),
      totalOperations: body.operationCounts.totalOperationsCount,
      ioCost: Number(Number(body.costs.breakdown.IOassociatedCost || 0).toFixed(2)),
      totalCost: Number(Number(body.costs.totalCost).toFixed(2))
    };
  } catch (error) {
    console.error('Error in calculateCostsForUser:', error);
    throw error;
  }
};
