import { resetPasswordLambda } from '../handler';
import { APIGatewayProxyEvent, Context, Callback, APIGatewayProxyResult } from 'aws-lambda';
import { createMockEvent, createMockContext, createTestUser } from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, seedTestUser } from '../../shared/testUtils/databaseLifecycle';

async function runLocalTest() {
  console.log('Running local test for Reset Password Lambda');
  
  try {
    await clearTestData();
    
    const testUser = await createTestUser({
      emailVerified: true,
      resetPasswordOTP: undefined,
      resetPasswordOTPExpiry: 0,
      lastResetPasswordRequestAt: Date.now() - 6 * 60 * 1000
    });
    
    await seedTestUser(testUser);
    
    const event = createMockEvent({
      email: testUser.email
    }, '/reset-password');
    
    const result = await resetPasswordLambda(event as APIGatewayProxyEvent);
    console.log('Result:', result);
    
    if (result.statusCode === 200) {
      const body = JSON.parse(result.body);
      console.log('Reset password email would be sent to:', testUser.email);
      console.log('Message:', body.data.message);
    }
    
    await clearTestData();
  } catch (error) {
    console.error('Error:', error);
  }
}

runLocalTest().catch(console.error); 