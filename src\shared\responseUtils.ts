import { APIGatewayProxyResult } from 'aws-lambda';
import { getCorsHeaders } from './corsHandler';

export const ErrorResponse = (statusCode: number, code: string, message: string): APIGatewayProxyResult => {
  console.log('Creating ErrorResponse:', { statusCode, code, message });
  const response = {
    statusCode,
    body: JSON.stringify({
      success: false,
      error: {
        code,
        message
      }
    }),
    headers: getCorsHeaders()
  };
  console.log('Generated response:', response);
  return response;
};

export const SuccessResponse = (statusCode: number, body: any) => {
  return {
    statusCode,
    headers: getCorsHeaders(),
    body: JSON.stringify({
      success: true,
      data: body
    })
  };
}; 