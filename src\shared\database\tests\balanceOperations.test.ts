import { updateParentAccountBalances, getParentAccountBalances } from '../balanceOperations';
import { clearTestData, initializeTestDatabase, seedTestUser } from '../../testUtils/databaseLifecycle';
import { createTestUser } from '../../testUtils/lambdaTestUtils';

describe('Balance Operations', () => {
  beforeAll(async () => {
    process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
    await initializeTestDatabase();
  });

  afterAll(() => {
    delete process.env.USER_DETAILS_TABLE_NAME;
  });

  beforeEach(async () => {
    await clearTestData();
  });

  it.skip('should update parent account balances', async () => {
    const parentUser = await createTestUser({
      accountType: 'parent',
      accountBalance: 3.00,
      availableBalance: 3.00,
      emailVerified: true
    });
    await seedTestUser(parentUser);

    const newBalances = {
      accountBalance: 2.60,
      availableBalance: 2.60
    };

    const balanceChange = -0.40; // 3.00 - 2.60
    await updateParentAccountBalances(parentUser.userID, balanceChange);
    const updatedBalances = await getParentAccountBalances(parentUser.userID);

    expect(updatedBalances).toEqual(newBalances);
  });

  it.skip('should handle non-existent user', async () => {
    const nonExistentUserID = 'non-existent-user';
    
    await expect(updateParentAccountBalances(nonExistentUserID, 1.00)).rejects.toThrow();
  });

  it.skip('should reject updates for child accounts', async () => {
    const childUser = await createTestUser({
      accountType: 'child',
      emailVerified: true
    });
    await seedTestUser(childUser);

    await expect(updateParentAccountBalances(childUser.userID, 1.00)).rejects.toThrow();
  });
}); 