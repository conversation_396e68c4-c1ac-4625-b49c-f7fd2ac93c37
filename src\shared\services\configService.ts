import { APIGatewayProxyEvent } from 'aws-lambda';
import { SecretsManager, SSM } from 'aws-sdk';

// Cache for parameters
const parameterCache: Record<string, {value: any, expiry: number}> = {};
const secretCache: Record<string, {value: string, expiry: number}> = {};
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Determines the environment based on the request host
 */
function determineEnvironment(event: APIGatewayProxyEvent): 'staging' | 'live' {
  const host = event.headers.Host || event.headers.host;
  return host?.includes('staging') ? 'staging' : 'live';
}

/**
 * Loads parameters from SSM Parameter Store
 */
async function loadParameters(environment: 'staging' | 'live'): Promise<Record<string, any>> {
  const parameterName = environment === 'staging' ? 'staging-AUTHIQA_PARAMS' : 'AUTHIQA_PARAMS';
  
  // Always clear cache when switching environments to ensure fresh data
  if (parameterCache[parameterName]) {
    const cachedEnvironment = parameterName.includes('staging') ? 'staging' : 'live';
    if (cachedEnvironment !== environment) {
      console.log(`Environment changed from ${cachedEnvironment} to ${environment}, clearing all caches`);
      clearParameterCache();
    } else {
      console.log(`Using cached parameters for ${environment} environment`);
      if (Date.now() < parameterCache[parameterName].expiry) {
        return parameterCache[parameterName].value;
      }
      console.log(`Cache expired for ${environment} environment, refreshing`);
    }
  }
  
  console.log(`Loading parameters from SSM for ${environment} environment`);
  
  // Load from SSM
  const ssm = new SSM({ region: process.env.REGION || 'eu-west-1' });
  const result = await ssm.getParameter({ 
    Name: parameterName, 
    WithDecryption: true 
  }).promise();
  
  if (!result.Parameter?.Value) {
    throw new Error(`Parameter ${parameterName} not found or empty`);
  }
  
  // Parse JSON value
  const parameters = JSON.parse(result.Parameter.Value);
  
  // Validate frontend URL for staging environment
  if (environment === 'staging' && parameters.FRONTEND_URL === 'https://authiqa.com') {
    console.log('WARNING: Correcting frontend URL for staging environment');
    parameters.FRONTEND_URL = 'https://staging.authiqa.com';
  }
  
  // Cache the result
  parameterCache[parameterName] = {
    value: parameters,
    expiry: Date.now() + CACHE_TTL
  };
  
  return parameters;
}

/**
 * Loads a secret from Secrets Manager
 */
async function loadSecret(secretName: string, environment: 'staging' | 'live'): Promise<string> {
  // Don't add environment prefix if the secretName already includes the environment
  const fullSecretName = secretName.includes('STAGING_') ? secretName : 
                         (environment === 'staging' ? `STAGING_${secretName}` : secretName);
  
  // Check cache first
  if (secretCache[fullSecretName] && Date.now() < secretCache[fullSecretName].expiry) {
    console.log(`Using cached secret ${fullSecretName}`);
    return secretCache[fullSecretName].value;
  }
  
  console.log(`Loading secret ${fullSecretName} from Secrets Manager`);
  
  // Load from Secrets Manager
  const secretsManager = new SecretsManager({ region: process.env.REGION || 'eu-west-1' });
  const result = await secretsManager.getSecretValue({ 
    SecretId: fullSecretName 
  }).promise();
  
  if (!result.SecretString) {
    throw new Error(`Secret ${fullSecretName} not found or empty`);
  }
  
  // Cache the result
  secretCache[fullSecretName] = {
    value: result.SecretString,
    expiry: Date.now() + CACHE_TTL
  };
  
  return result.SecretString;
}

/**
 * Application configuration interface
 */
export interface AppConfig {
  USER_DETAILS_TABLE_NAME: string;
  BILLING_TABLE_NAME: string;
  API_URL: string;
  SMTP_HOST: string;
  SMTP_PORT: string;
  SMTP_USER: string;
  REGION: string;
  OTP_EXPIRY: string;
  ENCRYPTION_KEY: string;
  NODE_ENV: string;
  FRONTEND_URL: string;
  PAYMENT_HISTORY_TABLE_NAME: string;
  COST_CALCULATOR_LAMBDA_NAME: string;
  [key: string]: any;
}

/**
 * Gets the application configuration based on the environment
 */
export async function getConfig(event: APIGatewayProxyEvent): Promise<AppConfig> {
  try {
    const environment = determineEnvironment(event);
    console.log(`[CONFIG] Getting configuration for ${environment} environment`);
    console.log(`[CONFIG] Request host: ${event.headers.Host || event.headers.host}`);
    
    const params = await loadParameters(environment);
    console.log(`[CONFIG] Loaded parameters for ${environment} environment:`, {
      frontendUrl: params.FRONTEND_URL,
      tableNames: {
        users: params.USER_DETAILS_TABLE_NAME,
        billing: params.BILLING_TABLE_NAME
      }
    });
    
    // Ensure all required properties are present with defaults
    return {
      USER_DETAILS_TABLE_NAME: params.USER_DETAILS_TABLE_NAME || process.env.USER_DETAILS_TABLE_NAME || '',
      BILLING_TABLE_NAME: params.BILLING_TABLE_NAME || process.env.BILLING_TABLE_NAME || '',
      API_URL: params.API_URL || process.env.API_URL || '',
      SMTP_HOST: params.SMTP_HOST || process.env.SMTP_HOST || '',
      SMTP_PORT: params.SMTP_PORT || process.env.SMTP_PORT || '',
      SMTP_USER: params.SMTP_USER || process.env.SMTP_USER || '',
      REGION: params.REGION || process.env.REGION || 'eu-west-1',
      OTP_EXPIRY: params.OTP_EXPIRY || process.env.OTP_EXPIRY || '',
      ENCRYPTION_KEY: params.ENCRYPTION_KEY || process.env.ENCRYPTION_KEY || '',
      NODE_ENV: params.NODE_ENV || process.env.NODE_ENV || 'production',
      FRONTEND_URL: params.FRONTEND_URL || process.env.FRONTEND_URL || '',
      PAYMENT_HISTORY_TABLE_NAME: params.PAYMENT_HISTORY_TABLE_NAME || process.env.PAYMENT_HISTORY_TABLE_NAME || '',
      COST_CALCULATOR_LAMBDA_NAME: params.COST_CALCULATOR_LAMBDA_NAME || process.env.COST_CALCULATOR_LAMBDA_NAME || '',
      ...params
    };
  } catch (error) {
    console.error('Error loading configuration:', error);
    // Fallback to environment variables
    console.log('Falling back to environment variables');
    return {
      USER_DETAILS_TABLE_NAME: process.env.USER_DETAILS_TABLE_NAME || '',
      BILLING_TABLE_NAME: process.env.BILLING_TABLE_NAME || '',
      API_URL: process.env.API_URL || '',
      SMTP_HOST: process.env.SMTP_HOST || '',
      SMTP_PORT: process.env.SMTP_PORT || '',
      SMTP_USER: process.env.SMTP_USER || '',
      REGION: process.env.REGION || 'eu-west-1',
      OTP_EXPIRY: process.env.OTP_EXPIRY || '',
      ENCRYPTION_KEY: process.env.ENCRYPTION_KEY || '',
      NODE_ENV: process.env.NODE_ENV || 'production',
      FRONTEND_URL: process.env.FRONTEND_URL || '',
      PAYMENT_HISTORY_TABLE_NAME: process.env.PAYMENT_HISTORY_TABLE_NAME || '',
      COST_CALCULATOR_LAMBDA_NAME: process.env.COST_CALCULATOR_LAMBDA_NAME || ''
    };
  }
}

/**
 * Gets a secret from Secrets Manager based on the environment
 */
export async function getSecret(secretName: string, event: APIGatewayProxyEvent): Promise<string> {
  try {
    const environment = determineEnvironment(event);
    return await loadSecret(secretName, environment);
  } catch (error) {
    console.error(`Error loading secret ${secretName}:`, error);
    // No fallback for secrets - they're critical
    throw new Error(`Failed to load secret ${secretName}`);
  }
}

/**
 * Gets a JSON secret from Secrets Manager and parses it
 */
export async function getJsonSecret(secretName: string, event: APIGatewayProxyEvent): Promise<any> {
  const secretString = await getSecret(secretName, event);
  return JSON.parse(secretString);
}

// Add this function to clear the cache
export function clearParameterCache(): void {
  console.log('Clearing parameter cache');
  Object.keys(parameterCache).forEach(key => delete parameterCache[key]);
  Object.keys(secretCache).forEach(key => delete secretCache[key]);
}

