import { TABLE_NAME } from '../../shared/database';
import { getUserByPublicKey, getUserByEmail, getUserByUsername } from '../../shared/database/userOperations';
import { ErrorResponse } from '../../shared/responseUtils';
import { User } from '../../shared/types';

export class ParentAccountService {
  public static async validateParentPublicKey(parentPublicKey: string, tableName?: string): Promise<{
    isValid: boolean;
    error?: ReturnType<typeof ErrorResponse>;
    parentUser?: User;
  }> {
    // Run format validation and user fetch in parallel
    console.log(`Validating parent public key: ${parentPublicKey} using table: ${tableName || TABLE_NAME}`);
    
    const [formatValid, parentUser] = await Promise.all([
      Promise.resolve(parentPublicKey.match(/^APK_[a-f0-9]{32}_\d+$/)),
      getUserByPublicKey(parentPublic<PERSON>ey, tableName) // Pass the tableName parameter here
    ]);

    console.log('Parent user lookup result:', {
      formatValid: !!formatValid,
      userFound: !!parentUser,
      tableName: tableName || TABLE_NAME,
      userDetails: parentUser ? {
        userID: parentUser.userID,
        publicKey: parentUser.publicKey,
        accountType: parentUser.accountType
      } : null
    });

    if (!formatValid) {
      return {
        isValid: false,
        error: ErrorResponse(400, 'INVALID_PARENT_PUBLIC_KEY_FORMAT', 'Invalid public key format')
      };
    }

    if (!parentUser || parentUser.availableBalance === undefined || parentUser.availableBalance <= 0) {
      const errorMessage = !parentUser 
        ? 'Parent account does not exist.' 
        : 'Parent account has insufficient balance for child operations';
      
      return {
        isValid: false,
        error: ErrorResponse(
          !parentUser ? 404 : 403,
          !parentUser ? 'PARENT_ACCOUNT_NOT_FOUND' : 'PARENT_ACCOUNT_INACTIVE',
          errorMessage
        )
      };
    }

    return {
      isValid: true,
      parentUser
    };
  }

  public static async validateParentChildRelation(
    email: string,
    username: string,
    parentUser: User
  ): Promise<{
    isValid: boolean;
    error?: ReturnType<typeof ErrorResponse>;
  }> {
    if (parentUser.email === email || parentUser.username === username) {
      return {
        isValid: false,
        error: ErrorResponse(400, 'INVALID_PARENT_ACCOUNT', 
          'Cannot create child account with same email/username as parent')
      };
    }

    return { isValid: true };
  }
}
