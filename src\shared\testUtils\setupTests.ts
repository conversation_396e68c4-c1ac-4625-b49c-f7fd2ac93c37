import * as dotenv from 'dotenv';
import { initializeTestDatabase, clearTestData } from './databaseLifecycle';

dotenv.config();
process.env.AWS_REGION = 'eu-west-1';
export default async function globalSetup() {
  console.log('Starting test setup...');
  if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
    throw new Error('AWS credentials not found in environment variables');
  }

  try {
    await initializeTestDatabase();
  } catch (error: any) {
    console.error('Failed to setup test database:', error);
    throw error;
  }
}

export async function globalTeardown() {
  try {
    await clearTestData();
    console.log('Test data cleaned up successfully');
  } catch (error) {
    console.error('Failed to clean up test data:', error);
  }
}
