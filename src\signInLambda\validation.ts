import { signInSchema } from '../shared/validation';
import { ErrorTypes } from '../shared/errorTypes';
import { APIGatewayProxyResult } from 'aws-lambda';

export const validateSignInRequest = (requestBody: any): APIGatewayProxyResult | null => {
  try {
    signInSchema.validateSync(requestBody, { abortEarly: false });
    return null;
  } catch (error) {
    if (error instanceof Error) {
      return ErrorTypes.INVALID_REQUEST_BODY();
    }
    return ErrorTypes.INVALID_REQUEST_BODY();
  }
};

