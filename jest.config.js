module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  globalSetup: '<rootDir>/src/shared/testUtils/setupTests.ts',
  globalTeardown: '<rootDir>/src/shared/testUtils/setupTests.ts',
  setupFilesAfterEnv: ['<rootDir>/src/shared/testUtils/setupTests.ts'],
  testTimeout: 30000,
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'clover', 'html', 'json-summary'],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.test.{ts,tsx}',
    '!src/**/*.spec.{ts,tsx}',
    '!src/**/tests/**',
    '!src/shared/testUtils/**',
    '!**/node_modules/**'
  ],
  transform: {
    '^.+\\.tsx?$': 'ts-jest'
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  roots: ['<rootDir>/src'],
  clearMocks: true,
  resetMocks: true,
  testPathIgnorePatterns: ['/node_modules/', '/dist/']
};
