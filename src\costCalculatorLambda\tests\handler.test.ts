import { costCalculator<PERSON><PERSON>b<PERSON> } from '../handler';
import { createMockEvent } from '../../shared/testUtils';
import { verifyToken } from '../../shared/utils/tokenUtils';
import { getChildAccountsByParent, getParentAccountOperations } from '../../shared/database/userOperations';
import { OPERATION_COSTS, COST_CALCULATOR_OPERATIONS, AUTHIQA_MARGIN, PRICES } from '../../shared/constants';
import { getUserByPublicKey } from '../../shared/database/userOperations';
import { getParentAccountBalances, updateParentAccountBalances } from '../../shared/database/balanceOperations';

jest.mock('../../shared/utils/tokenUtils');
jest.mock('../../shared/database/userOperations');

describe('Cost Calculator Lambda', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.INTERNAL_SERVICE_KEY = 'test-internal-key';
    (verifyToken as jest.Mock).mockReturnValue({
      apiKey: 'parent-key-123',
      accountType: 'parent'
    });
  });

  afterEach(() => {
    delete process.env.INTERNAL_SERVICE_KEY;
  });

  it.skip('should calculate costs correctly for typical usage', async () => {
    const parentOps = {
      emailConfirmation: 1,
      resendConfirmation: 0,
      resetPassword: 1,
      updatePassword: 1,
      signIn: 10,
      organizationUpdate: 2,
      organizationDetailsRetrieval: 5,
      childAccountsListRetrieval: 8,
      childAccounts: 2
    };

    const childOps = {
      emailConfirmation: 2,
      resendConfirmation: 1,
      resetPassword: 1,
      updatePassword: 1,
      signIn: 15,
      organizationUpdate: 0,
      organizationDetailsRetrieval: 0,
      childAccountsListRetrieval: 0,
      childAccounts: 0
    };

    (getParentAccountOperations as jest.Mock).mockResolvedValue(parentOps);
    (getChildAccountsByParent as jest.Mock).mockResolvedValue({
      operationCounts: childOps,
      totalAccounts: 2
    });

    const event = createMockEvent({}, '/parent/cost-analysis');
    event.headers = { Authorization: 'Bearer valid-token' };
    
    const response = await costCalculatorLambda(event as any);
    const body = JSON.parse(response.body);

    // Test total accounts
    expect(body.operationCounts.totalAccounts).toBe(3); // 2 children + 1 parent

    // Test regular operations
    const totalEmailConfirmations = parentOps.emailConfirmation + childOps.emailConfirmation;
    const expectedEmailCost = 
      (totalEmailConfirmations * OPERATION_COSTS.emailConfirmation.reads * 0.1415 + 
       totalEmailConfirmations * OPERATION_COSTS.emailConfirmation.writes * 0.705) / 1000000;
    
    expect(body.costs.breakdown.emailConfirmation).toBeCloseTo(expectedEmailCost, 10);

    // Test parent-only operations
    const orgUpdateCost = 
      (parentOps.organizationUpdate * OPERATION_COSTS.organizationUpdate.reads * 0.1415 + 
       parentOps.organizationUpdate * OPERATION_COSTS.organizationUpdate.writes * 0.705) / 1000000;
    
    expect(body.costs.breakdown.organizationUpdate).toBeCloseTo(orgUpdateCost, 10);

    // Test account creation costs
    const totalAccounts = body.operationCounts.totalAccounts;
    const expectedAccountCost = 
      (totalAccounts * OPERATION_COSTS.childAccounts.reads * 0.1415 + 
       totalAccounts * OPERATION_COSTS.childAccounts.writes * 0.705) / 1000000;
    
    expect(body.costs.breakdown.childAccounts).toBeCloseTo(expectedAccountCost, 10);
  });

  it.skip('should reject non-parent accounts', async () => {
    (verifyToken as jest.Mock).mockReturnValue({ accountType: 'child' });
    
    const event = createMockEvent({}, '/parent/cost-analysis');
    event.headers = { Authorization: 'Bearer token' };
    
    const response = await costCalculatorLambda(event as any);
    expect(response.statusCode).toBe(401);
  });

  it.skip('should reject requests without authorization', async () => {
    const event = createMockEvent({}, '/parent/cost-analysis');
    const response = await costCalculatorLambda(event as any);
    expect(response.statusCode).toBe(401);
  });

  it.skip('should handle database errors gracefully', async () => {
    const dbError = new Error('Database connection failed');
    (getParentAccountOperations as jest.Mock).mockRejectedValue(dbError);
    
    const event = createMockEvent({}, '/parent/cost-analysis');
    event.headers = { Authorization: 'Bearer valid-token' };
    
    const response = await costCalculatorLambda(event as any);
    expect(response.statusCode).toBe(500);
  });

  it.skip('should calculate account creation costs correctly', async () => {
    const parentOps = {
      emailConfirmation: 0,
      resendConfirmation: 0,
      resetPassword: 0,
      updatePassword: 0,
      signIn: 0,
      organizationUpdate: 0,
      organizationDetailsRetrieval: 0,
      childAccountsListRetrieval: 0,
      childAccounts: 5  // Parent created 5 child accounts
    };

    const childOps = {
      emailConfirmation: 0,
      resendConfirmation: 0,
      resetPassword: 0,
      updatePassword: 0,
      signIn: 0,
      organizationUpdate: 0,
      organizationDetailsRetrieval: 0,
      childAccountsListRetrieval: 0,
      childAccounts: 5  // 5 child accounts exist
    };

    (getParentAccountOperations as jest.Mock).mockResolvedValue(parentOps);
    (getChildAccountsByParent as jest.Mock).mockResolvedValue({
      operationCounts: childOps,
      totalAccounts: 5
    });

    const event = createMockEvent({}, '/parent/cost-analysis');
    event.headers = { Authorization: 'Bearer valid-token' };
    
    const response = await costCalculatorLambda(event as any);
    const body = JSON.parse(response.body);

    // 6 total accounts (5 children + 1 parent) * (2 reads + 1 write) per account
    const expectedAccountCost = 
      (6 * OPERATION_COSTS.childAccounts.reads * 0.1415 + 
       6 * OPERATION_COSTS.childAccounts.writes * 0.705) / 1000000;

    expect(body.costs.breakdown.childAccounts).toBeCloseTo(expectedAccountCost, 10);
  });

  it.skip('should include cost calculator operations in total cost', async () => {
    const parentOps = {
      emailConfirmation: 1,
      resendConfirmation: 0,
      resetPassword: 0,
      updatePassword: 0,
      signIn: 5,
      organizationUpdate: 1,
      organizationDetailsRetrieval: 2,
      childAccountsListRetrieval: 3,
      childAccounts: 2
    };

    const childOps = {
      emailConfirmation: 2,
      resendConfirmation: 1,
      resetPassword: 0,
      updatePassword: 0,
      signIn: 10,
      organizationUpdate: 0,
      organizationDetailsRetrieval: 0,
      childAccountsListRetrieval: 0,
      childAccounts: 2
    };

    (getParentAccountOperations as jest.Mock).mockResolvedValue(parentOps);
    (getChildAccountsByParent as jest.Mock).mockResolvedValue({
      operationCounts: childOps,
      totalAccounts: 2
    });

    const event = createMockEvent({}, '/parent/cost-analysis');
    event.headers = { Authorization: 'Bearer valid-token' };
    
    const response = await costCalculatorLambda(event as any);
    const body = JSON.parse(response.body);

    // Calculate expected cost calculator operations cost using COST_CALCULATOR_OPERATIONS
    const expectedCostCalculatorCost = 
      (COST_CALCULATOR_OPERATIONS.reads * PRICES.read * COST_CALCULATOR_OPERATIONS.expectedCalls) / 1000000;

    expect(body.costs.breakdown.costCalculator).toBeCloseTo(expectedCostCalculatorCost, 10);
    expect(body.costs.total).toBeGreaterThan(body.costs.breakdown.costCalculator);
  });

  it.skip('should calculate margin correctly', async () => {
    const parentOps = {
      emailConfirmation: 1,
      resendConfirmation: 0,
      resetPassword: 0,
      updatePassword: 0,
      signIn: 5,
      organizationUpdate: 1,
      organizationDetailsRetrieval: 2,
      childAccountsListRetrieval: 3,
      childAccounts: 2
    };

    const childOps = {
      emailConfirmation: 2,
      resendConfirmation: 1,
      resetPassword: 0,
      updatePassword: 0,
      signIn: 10,
      organizationUpdate: 0,
      organizationDetailsRetrieval: 0,
      childAccountsListRetrieval: 0,
      childAccounts: 2
    };

    (getParentAccountOperations as jest.Mock).mockResolvedValue(parentOps);
    (getChildAccountsByParent as jest.Mock).mockResolvedValue({
      operationCounts: childOps,
      totalAccounts: 2
    });

    const event = createMockEvent({}, '/parent/cost-analysis');
    event.headers = { Authorization: 'Bearer valid-token' };
    
    const response = await costCalculatorLambda(event as any);
    const body = JSON.parse(response.body);

    // Verify margin calculation
    expect(body.costs.baseCost).toBeDefined();
    expect(body.costs.margin).toBeCloseTo(body.costs.baseCost * AUTHIQA_MARGIN, 10);
    expect(body.costs.total).toBeCloseTo(body.costs.baseCost * (1 + AUTHIQA_MARGIN), 10);
  });

  it.skip('should accept internal service calls with valid API key', async () => {
    const event = createMockEvent({}, '/parent/cost-analysis');
    event.headers = {
      'x-internal-service': 'test-internal-key',
      'x-api-key': 'valid-api-key'
    };
    
    (getUserByPublicKey as jest.Mock).mockResolvedValue({
      accountType: 'parent',
      apiKey: 'valid-api-key'
    });
    
    const response = await costCalculatorLambda(event as any);
    expect(response.statusCode).toBe(200);
  });

  it.skip('should reject internal service calls with invalid service key', async () => {
    const event = createMockEvent({}, '/parent/cost-analysis');
    event.headers = {
      'x-internal-service': 'invalid-key',
      'x-api-key': 'valid-api-key'
    };
    
    const response = await costCalculatorLambda(event as any);
    expect(response.statusCode).toBe(401);
  });

  it.skip('should include balance information in the response', async () => {
    // Mock user and balance data
    (getUserByPublicKey as jest.Mock).mockResolvedValue({
      userID: 'test-user-id',
      accountType: 'parent',
      apiKey: 'valid-api-key'
    });
    
    (getParentAccountBalances as jest.Mock).mockResolvedValue({
      accountBalance: 10.00,
      availableBalance: 8.50
    });
    
    // Mock the update function
    (updateParentAccountBalances as jest.Mock).mockResolvedValue(undefined);
    
    // Set up the event
    const event = {
      headers: {
        Authorization: 'Bearer valid-token'
      }
    };
    
    // Call the handler
    const response = await costCalculatorLambda(event as any);
    const body = JSON.parse(response.body);
    
    // Verify balance information is included
    expect(body.balance).toBeDefined();
    expect(body.balance.accountBalance).toBe(10.00);
    expect(body.balance.availableBalance).toBeLessThan(10.00);
    expect(body.balance.currentCharges).toBeDefined();
    expect(body.balance.usagePercentage).toBeDefined();
    expect(body.balance.lowBalanceAlert).toBeDefined();
    
    // Verify the balance was updated
    expect(updateParentAccountBalances).toHaveBeenCalled();
  });
}); 