import { dynamoD<PERSON>, TABLE_NAME, dynamoDBClient } from '../database';
import { Delete<PERSON>ommand, ScanCommand, PutCommand } from "@aws-sdk/lib-dynamodb";
import { DescribeTableCommand } from "@aws-sdk/client-dynamodb";
import { hashPassword } from '../utils/passwordUtils';
import { generatePublicKey } from '../utils/apiKeyUtils';

interface User {
  userID: string;
  username?: string;
  email: string;
  password: string;
  emailVerified: boolean;
  accountStatus: string;
  createdAt: number;
  OTP?: string;
  OTPExpiry?: number;
  loginAttempts: number;
  lastLoginAttempt: number;
  lastOTPSentAt?: number;
  publicKey: string; // Updated from publicKey
  parentAccount: string;
}

let isTableInitialized = false;

export async function initializeTestDatabase() {
  if (isTableInitialized) {
    return;
  }

  try {
    console.log('Initializing test database...');
    
    // Test connection first
    const isConnected = await testDatabaseConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to database');
    }
    
    isTableInitialized = true;
    console.log('Test database initialized successfully');
  } catch (error) {
    console.error('Failed to initialize test database:', error);
    throw error;
  }
}

async function checkTableExists(tableName: string): Promise<boolean> {
  try {
    await dynamoDBClient.send(new DescribeTableCommand({ TableName: tableName }));
    return true;
  } catch (error: any) {
    if (error.name === 'ResourceNotFoundException') {
      return false;
    }
    throw error;
  }
}

export async function clearTestData() {
  if (!isTableInitialized) {
    return;
  }

  try {
    const result = await dynamoDBClient.send(new ScanCommand({
      TableName: TABLE_NAME
    }));

    if (result.Items) {
      for (const item of result.Items) {
        await dynamoDBClient.send(new DeleteCommand({
          TableName: TABLE_NAME,
          Key: { userID: item.userID }
        }));
      }
    }
  } catch (error) {
    console.error('Failed to clear test data:', error);
    throw error;
  }
}

export async function seedTestUser(overrides: Partial<User> = {}): Promise<User> {
  const defaultUser: User = {
    userID: `USR_${Date.now()}`,
    email: `test-${Date.now()}@example.com`,
    username: `testuser-${Date.now()}`,
    password: await hashPassword('Password123!'),
    emailVerified: true,
    accountStatus: 'active',
    createdAt: Date.now(),
    loginAttempts: 0,
    lastLoginAttempt: 0,
    publicKey: generatePublicKey(), // Updated from publicKey
    parentAccount: 'ROOT'
  };
  
  const user = { ...defaultUser, ...overrides };
  
  await dynamoDBClient.send(new PutCommand({
    TableName: TABLE_NAME,
    Item: user
  }));
  
  return user;
}

export async function testDatabaseConnection() {
  try {
    const result = await dynamoDBClient.send(new DescribeTableCommand({ TableName: TABLE_NAME }));
    console.log('✅ Database connection successful');
    
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
} 
