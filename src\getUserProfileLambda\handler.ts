import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ErrorTypes } from '../shared/errorTypes';
import { SuccessResponse } from '../shared/responseUtils';
import { getUserById } from '../shared/database/userOperations';
import { addCorsHeaders } from '../shared/corsHandler';
import { verifyToken } from '../shared/utils/tokenUtils';
import { getConfig } from '../shared/services/configService';

export const getUserProfileLambda = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  const startTime = Date.now();
  console.log('[GET-USER-PROFILE] Lambda execution started', {
    timestamp: new Date().toISOString(),
    requestId: event.requestContext?.requestId,
    sourceIp: event.requestContext?.identity?.sourceIp,
    userAgent: event.headers['User-Agent'] || event.headers['user-agent']
  });

  // Load configuration from the service
  const config = await getConfig(event);

  console.log('[GET-USER-PROFILE] Lambda invoked with headers:', {
    host: event.headers.Host || event.headers.host,
    origin: event.headers.Origin || event.headers.origin,
    hasAuthHeader: !!(event.headers.Authorization || event.headers.authorization)
  });

  console.log('[CONFIG] GetUserProfileLambda using configuration service', {
    environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
    userTable: config.USER_DETAILS_TABLE_NAME,
    nodeEnv: config.NODE_ENV
  });

  try {
    // Validate JWT authentication
    console.log('[GET-USER-PROFILE] Starting JWT authentication validation');
    const authHeader = event.headers.Authorization || event.headers.authorization;
    if (!authHeader) {
      console.log('[GET-USER-PROFILE] Missing authorization header - returning UNAUTHORIZED');
      return addCorsHeaders(ErrorTypes.UNAUTHORIZED(), event);
    }

    console.log('[GET-USER-PROFILE] Authorization header found, extracting token');
    const token = authHeader.replace('Bearer ', '');
    console.log('[GET-USER-PROFILE] Token extracted, length:', token.length);

    console.log('[GET-USER-PROFILE] Verifying JWT token');
    const decodedToken = await verifyToken(token, config.USER_DETAILS_TABLE_NAME);
    if (!decodedToken) {
      console.log('[GET-USER-PROFILE] Token verification failed - invalid or expired token');
      return addCorsHeaders(ErrorTypes.UNAUTHORIZED(), event);
    }

    console.log('[GET-USER-PROFILE] Token verified successfully', {
      userID: decodedToken.userID,
      email: decodedToken.email,
      accountType: decodedToken.accountType,
      parentAccount: decodedToken.parentAccount
    });

    // Get complete user data
    console.log('[GET-USER-PROFILE] Fetching user data from database', {
      userID: decodedToken.userID,
      tableName: config.USER_DETAILS_TABLE_NAME
    });

    const user = await getUserById(decodedToken.userID, config.USER_DETAILS_TABLE_NAME);
    if (!user) {
      console.log(`[GET-USER-PROFILE] User not found in database`, {
        userID: decodedToken.userID,
        email: decodedToken.email
      });
      return addCorsHeaders(ErrorTypes.USER_NOT_FOUND(), event);
    }

    console.log('[GET-USER-PROFILE] User found in database', {
      userID: user.userID,
      email: user.email,
      accountType: user.accountType,
      accountStatus: user.accountStatus,
      emailVerified: user.emailVerified,
      createdAt: user.createdAt
    });

    // Validate account status
    if (user.accountStatus !== 'active') {
      console.log(`[GET-USER-PROFILE] Account is not active`, {
        userID: user.userID,
        currentStatus: user.accountStatus,
        expectedStatus: 'active'
      });
      return addCorsHeaders(ErrorTypes.ACCOUNT_INACTIVE(), event);
    }

    console.log(`[GET-USER-PROFILE] Account validation passed - preparing response data`, {
      userID: user.userID,
      accountType: user.accountType,
      hasOrganization: !!user.organizationName,
      isPromoted: !!user.isPromotedAccount
    });

    // Prepare response data with field counts for logging
    const responseData = {
      message: 'User profile retrieved successfully',
      user: {
        // Core Identity
        userID: user.userID,
        username: user.username,
        email: user.email,
        publicKey: user.publicKey,

        // Account Information
        accountType: user.accountType,
        parentAccount: user.parentAccount,
        accountStatus: user.accountStatus,
        emailVerified: user.emailVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        lastLogin: user.lastLogin,

        // Organization Details
        organizationName: user.organizationName || null,
        organizationUrl: user.organizationUrl || null,
        authUrls: user.authUrls || null,
        domainRestrictionEnabled: user.domainRestrictionEnabled !== undefined ? user.domainRestrictionEnabled : true,
        emailVerificationRequired: user.emailVerificationRequired !== undefined ? user.emailVerificationRequired : false,

        // Promoted Account Information
        isPromotedAccount: user.isPromotedAccount || false,
        promotedAt: user.promotedAt || null,
        organizationId: user.organizationId || null,

        // Authentication Details
        authProvider: user.authProvider || 'email',
        lastLoginProvider: user.lastLoginProvider || null,
        googleId: user.googleId || null,
        githubId: user.githubId || null,

        // Billing Information (for parent accounts)
        ...(user.accountType === 'parent' && {
          accountBalance: user.accountBalance || 0,
          availableBalance: user.availableBalance || 0
        }),

        // Operation Counters
        organizationUpdateCount: user.organizationUpdateCount || 0,
        emailConfirmationCount: user.emailConfirmationCount || 0,
        resendEmailCount: user.resendEmailCount || 0,
        resetPasswordRequestCount: user.resetPasswordRequestCount || 0,
        passwordUpdateCount: user.passwordUpdateCount || 0,
        signInCount: user.signInCount || 0,
        organizationDetailsRetrievalCount: user.organizationDetailsRetrievalCount || 0,
        childAccountsListRetrievalCount: user.childAccountsListRetrievalCount || 0,

        // Security Information
        lastPasswordChanged: user.lastPasswordChanged || null,
        loginAttempts: user.loginAttempts || 0,
        lastLoginAttempt: user.lastLoginAttempt || null,

        // Google SSO Configuration (for parent accounts)
        ...(user.accountType === 'parent' && user.googleSsoConfig && {
          googleSsoConfig: {
            enabled: user.googleSsoConfig.enabled || false,
            clientId: user.googleSsoConfig.clientId || ''
            // Note: clientSecret is intentionally excluded for security
          }
        }),

        // GitHub SSO Configuration (for parent accounts)
        ...(user.accountType === 'parent' && user.githubSsoConfig && {
          githubSsoConfig: {
            enabled: user.githubSsoConfig.enabled || false,
            clientId: user.githubSsoConfig.clientId || ''
            // Note: clientSecret is intentionally excluded for security
          }
        }),

        // Notification Timestamps
        lastLowBalanceNotificationAt: user.lastLowBalanceNotificationAt || null,
        lastCriticalBalanceNotificationAt: user.lastCriticalBalanceNotificationAt || null,
        lastDepletedBalanceNotificationAt: user.lastDepletedBalanceNotificationAt || null,

        // Account Lock Information
        lockedUntil: user.lockedUntil || null,

        // Reset Information (non-sensitive parts only)
        lastResetPasswordRequestAt: user.lastResetPasswordRequestAt || null

        // NOTE: The following fields are INTENTIONALLY EXCLUDED for security:
        // - password (hashed password)
        // - jwtSecret (JWT signing secret)
        // - verificationToken (email verification token)
        // - verificationTokenExpiry (token expiry)
        // - secretKey (encryption key)
        // - iv (initialization vector)
        // - resetPasswordOTP (password reset token)
        // - resetPasswordOTPExpiry (reset token expiry)
        // - googleSsoConfig.clientSecret (encrypted secret)
        // - githubSsoConfig.clientSecret (encrypted secret)
      }
    };

    // Log successful response preparation
    const executionTime = Date.now() - startTime;
    console.log('[GET-USER-PROFILE] Response prepared successfully', {
      userID: user.userID,
      accountType: user.accountType,
      responseSize: JSON.stringify(responseData).length,
      executionTimeMs: executionTime,
      fieldsIncluded: {
        coreIdentity: 4,
        accountInfo: 7,
        organizationDetails: 5,
        promotedAccountInfo: 3,
        authDetails: 4,
        billingInfo: user.accountType === 'parent' ? 2 : 0,
        operationCounters: 8,
        securityInfo: 3,
        googleSsoConfig: (user.accountType === 'parent' && user.googleSsoConfig) ? 2 : 0,
        githubSsoConfig: (user.accountType === 'parent' && user.githubSsoConfig) ? 2 : 0,
        notificationTimestamps: 3,
        accountLockInfo: 1,
        resetInfo: 1
      }
    });

    console.log('[GET-USER-PROFILE] Returning successful response', {
      statusCode: 200,
      userID: user.userID,
      executionTimeMs: executionTime
    });

    // Return complete user profile data
    return addCorsHeaders(SuccessResponse(200, responseData), event);

  } catch (error) {
    const executionTime = Date.now() - startTime;
    console.error('[GET-USER-PROFILE] Error retrieving user profile', {
      error: error instanceof Error ? error.message : 'Unknown error',
      errorType: error instanceof Error ? error.constructor.name : typeof error,
      executionTimeMs: executionTime,
      stack: error instanceof Error ? error.stack : undefined
    });

    // For detailed errors in local environment, log the specific error
    if (config.NODE_ENV === 'local') {
      console.error('[GET-USER-PROFILE] Detailed local error:', {
        fullError: error,
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });
    }

    console.log('[GET-USER-PROFILE] Returning error response', {
      statusCode: 500,
      executionTimeMs: executionTime
    });

    return addCorsHeaders(ErrorTypes.INTERNAL_ERROR(), event);
  }
};

// Export for SAM template compatibility
export const lambda = getUserProfileLambda;
