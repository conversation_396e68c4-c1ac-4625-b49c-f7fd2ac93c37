import { APIGatewayProxyEvent, Context, Callback, APIGatewayProxyResult } from 'aws-lambda';
import { signInLambda } from '../handler';
import * as jwt from 'jsonwebtoken';
import { manageLoginAttempts, LoginAttemptResult } from '../../shared/loginAttempts';
import { 
  createMockEvent, 
  createMockContext,
  createTestUser,
  expectErrorResponse,
  expectSuccessResponse,
  VALID_TEST_PASSWORD
} from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { hashPassword } from '../../shared/utils/passwordUtils';
import { generatePublicKey } from '../../shared/utils/apiKeyUtils';
import { getUserByEmail } from '../../shared/database/userOperations';
import { getTestUser } from '../../shared/testUtils/databaseTestUtils';
import { verifyPassword } from '../../shared/utils/passwordUtils';
import { v4 as uuidv4 } from 'uuid';
import { incrementSignInCount } from '../../shared/database/userOperations';
import { initializeTestDatabase } from '../../shared/testUtils/databaseLifecycle';

jest.mock('jsonwebtoken');
jest.mock('../../shared/loginAttempts');
jest.mock('../../shared/utils/passwordUtils');
jest.mock('../../shared/utils/tokenUtils');

describe('SignIn Lambda Handler', () => {
  beforeAll(async () => {
    process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
    process.env.ENCRYPTION_KEY = "dBG9kF7bH5cJ2mN8pQ4sT6wX0yZ3vA1eI/nL+uM9oR4=";
    await initializeTestDatabase();
  });

  afterAll(async () => {
    delete process.env.USER_DETAILS_TABLE_NAME;
    delete process.env.ENCRYPTION_KEY;
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Mock login attempts
    (manageLoginAttempts as jest.Mock).mockResolvedValue({ 
      locked: false, 
      attempts: 0 
    });
    
    // Mock JWT token generation
    (jwt.sign as jest.Mock).mockReturnValue('mock.jwt.token');
    
    // Mock password verification
    (verifyPassword as jest.Mock).mockResolvedValue(true);
  });

  it.skip('should sign in parent account successfully', async () => {
    const hashedPassword = await hashPassword(VALID_TEST_PASSWORD);
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent',
      emailVerified: true,
      accountStatus: 'active',
      password: hashedPassword
    });
    await seedTestUser(parentUser);

    const event = createMockEvent({
      email: parentUser.email,
      password: VALID_TEST_PASSWORD
    }, '/signin');

    const response = await signInLambda(event as APIGatewayProxyEvent);
    expectSuccessResponse(response, 200, {
      token: 'mock.jwt.token',
      user: {
        userID: parentUser.userID,
        email: parentUser.email,
        username: parentUser.username,
        accountType: 'parent',
        parentAccount: 'ROOT',
        publicKey: parentUser.publicKey
      }
    });
  });

  it.skip('should sign in child account successfully with parentpublicKey', async () => {
    const hashedPassword = await hashPassword(VALID_TEST_PASSWORD);
    const parentpublicKey = generatePublicKey();
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent',
      publicKey: parentpublicKey
    });
    await seedTestUser(parentUser);

    const childUser = await createTestUser({
      username: 'childuser',
      email: '<EMAIL>',
      parentAccount: parentpublicKey,
      accountType: 'child',
      emailVerified: true,
      accountStatus: 'active',
      password: hashedPassword
    });
    await seedTestUser(childUser);

    const event = createMockEvent({
      email: childUser.email,
      password: VALID_TEST_PASSWORD,
      parentpublicKey: parentpublicKey
    }, '/signin');

    const response = await signInLambda(event as APIGatewayProxyEvent);
    expectSuccessResponse(response, 200, {
      token: 'mock.jwt.token',
      user: {
        userID: childUser.userID,
        email: childUser.email,
        username: childUser.username,
        accountType: 'child',
        parentAccount: parentpublicKey,
        publicKey: childUser.publicKey
      }
    });
  });

  it.skip('should return error if trying to sign in child account without parentpublicKey', async () => {
    const hashedPassword = await hashPassword(VALID_TEST_PASSWORD);
    const parentpublicKey = generatePublicKey();
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent',
      publicKey: parentpublicKey,
      emailVerified: true,
      accountStatus: 'active'
    });
    await seedTestUser(parentUser);

    const childUser = await createTestUser({
      username: 'childuser',
      email: '<EMAIL>',
      parentAccount: parentpublicKey,
      accountType: 'child',
      emailVerified: true,
      accountStatus: 'active',
      password: hashedPassword
    });
    await seedTestUser(childUser);

    const event = createMockEvent({
      email: childUser.email,
      password: VALID_TEST_PASSWORD
    }, '/signin');

    const response = await signInLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'MISSING_PARENT_PUBLIC_KEY', 'Parent public key is required for child accounts');
  });

  it.skip('should return error if child account provides incorrect parentpublicKey', async () => {
    const hashedPassword = await hashPassword(VALID_TEST_PASSWORD);
    const parentpublicKey = generatePublicKey();
    const wrongParentpublicKey = generatePublicKey();
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent',
      publicKey: parentpublicKey,
      emailVerified: true,
      accountStatus: 'active'
    });
    await seedTestUser(parentUser);

    const childUser = await createTestUser({
      username: 'childuser',
      email: '<EMAIL>',
      parentAccount: parentpublicKey,
      accountType: 'child',
      emailVerified: true,
      accountStatus: 'active',
      password: hashedPassword
    });
    await seedTestUser(childUser);

    const event = createMockEvent({
      email: childUser.email,
      password: VALID_TEST_PASSWORD,
      parentpublicKey: wrongParentpublicKey
    }, '/signin');

    const response = await signInLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 401, 'INVALID_PARENT_PUBLIC_KEY', 'Invalid parent public key');
  });

  it.skip('should return error if parent account provides parentpublicKey', async () => {
    const hashedPassword = await hashPassword(VALID_TEST_PASSWORD);
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent',
      emailVerified: true,
      accountStatus: 'active',
      password: hashedPassword
    });
    await seedTestUser(parentUser);

    const event = createMockEvent({
      email: parentUser.email,
      password: VALID_TEST_PASSWORD,
      parentpublicKey: generatePublicKey()
    }, '/signin');

    const response = await signInLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_REQUEST', 'Parent public key should not be provided for parent accounts');
  });

  it.skip('should return an error for unverified email', async () => {
    const testUser = await createTestUser({
      emailVerified: false,
      accountStatus: 'active',
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email,
      password: VALID_TEST_PASSWORD
    }, '/signin');

    const response = await signInLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 403, 'EMAIL_NOT_VERIFIED', 'Please verify your email address');
  });

  it.skip('should return an error for inactive account', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      accountStatus: 'inactive',
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email,
      password: VALID_TEST_PASSWORD
    }, '/signin');

    const response = await signInLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 403, 'ACCOUNT_INACTIVE', 'Your account is not active');
  });

  it.skip('should return an error for locked account', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      accountStatus: 'active',
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(testUser);
    (manageLoginAttempts as jest.Mock).mockResolvedValue({ 
      locked: true,
      attempts: 5
    } as LoginAttemptResult);

    const event = createMockEvent({
      email: testUser.email,
      password: VALID_TEST_PASSWORD
    }, '/signin');

    const response = await signInLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 403, 'ACCOUNT_LOCKED', 'Your account has been locked due to too many failed login attempts');
  });

  it.skip('should return error for invalid credentials', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      accountStatus: 'active',
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email,
      password: 'Wrong@Password123'
    }, '/signin');

    const response = await signInLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 401, 'INVALID_CREDENTIALS', 'Invalid email or password');
  });

  it.skip('should return an error for missing request body', async () => {
    const event = createMockEvent({}, '/signin');
    event.body = '';

    const response = await signInLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'MISSING_REQUEST_BODY', 'Request body is required');
  });

  it.skip('should return an error for invalid JSON in request body', async () => {
    const event = createMockEvent({}, '/signin');
    event.body = 'invalid json';

    const response = await signInLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_REQUEST_BODY', 'Invalid JSON in request body');
  });

  it.skip('should increment sign in count on successful sign in', async () => {
    const hashedPassword = await hashPassword(VALID_TEST_PASSWORD);
    const testUser = await createTestUser({
      username: 'testuser',
      email: '<EMAIL>',
      password: hashedPassword,
      accountType: 'parent',
      parentAccount: 'ROOT',
      emailVerified: true,
      accountStatus: 'active',
      signInCount: 0
    });
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email,
      password: VALID_TEST_PASSWORD
    }, '/signin');

    const response = await signInLambda(event as APIGatewayProxyEvent);
    
    expectSuccessResponse(response, 200, {
      token: 'mock.jwt.token',
      user: {
        userID: testUser.userID,
        email: testUser.email,
        username: testUser.username,
        accountType: 'parent',
        parentAccount: 'ROOT',
        publicKey: testUser.publicKey,
        organizationName: null,
        organizationUrl: null,
        organizationUpdateCount: 0
      }
    });

    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.signInCount).toBe(1);
  });

  it.skip('should increment sign in count even on failed sign in', async () => {
    // Override the verifyPassword mock for this test
    (verifyPassword as jest.Mock).mockResolvedValue(false);
    
    const hashedPassword = await hashPassword(VALID_TEST_PASSWORD);
    const testUser = await createTestUser({
      username: 'testuser2',
      email: '<EMAIL>',
      password: hashedPassword,
      accountType: 'parent',
      parentAccount: 'ROOT',
      emailVerified: true,
      accountStatus: 'active',
      signInCount: 0
    });
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email,
      password: 'wrongPassword123'
    }, '/signin');

    const response = await signInLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 401, 'INVALID_CREDENTIALS', 'Invalid email or password');

    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.signInCount).toBe(1);
  });

  it.skip('should continue sign in process even with database errors when incrementing sign in count', async () => {
    const testUser = await createTestUser({
      username: 'testuser3',
      email: '<EMAIL>',
      password: await hashPassword(VALID_TEST_PASSWORD),
      accountType: 'parent',
      parentAccount: 'ROOT',
      emailVerified: true,
      accountStatus: 'active'
    });
    await seedTestUser(testUser);  // Make sure to seed the user

    // Mock the database error after user exists
    jest.spyOn(require('../../shared/database/userOperations'), 'incrementSignInCount')
      .mockRejectedValueOnce(new Error('Database error'));

    const event = createMockEvent({
      email: testUser.email,
      password: VALID_TEST_PASSWORD
    }, '/signin');

    const response = await signInLambda(event as APIGatewayProxyEvent);
    expect(response.statusCode).toBe(200);
  });
});
