import { APIGatewayProxyEvent } from 'aws-lambda';
import { getOrganizationDetailsLambda } from '../handler';
import { clearTestData, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { createTestUser } from '../../shared/testUtils/lambdaTestUtils';

describe('Organization Details Lambda CORS Tests', () => {
    beforeEach(async () => {
        await clearTestData();
    });

    it.skip('should handle requests from any origin', async () => {
        // Create test user
        const testUser = await createTestUser({
            accountType: 'parent',
            parentAccount: 'ROOT'
        });
        await seedTestUser(testUser);

        // Test different origins
        const origins = [
            'https://bkawk.github.io',
            'http://localhost:3000',
            'https://custom-domain.com',
            'https://app.example.org'
        ];

        for (const testOrigin of origins) {
            const event = {
                headers: {
                    'origin': testOrigin,
                    'X-Public-Key': testUser.publicKey
                },
                httpMethod: 'GET'
            } as unknown as APIGatewayProxyEvent;

            const response = await getOrganizationDetailsLambda(event);

            // Verify CORS headers exist and are correct
            expect(response.headers).toBeDefined();
            expect(response.headers!['Access-Control-Allow-Origin']).toBe(testOrigin);
            expect(response.headers!['Access-Control-Allow-Credentials']).toBe('true');
        }
    });

    it.skip('should handle preflight requests from any origin', async () => {
        const origins = [
            'https://bkawk.github.io',
            'http://localhost:3000',
            'https://custom-domain.com'
        ];

        for (const testOrigin of origins) {
            const event = {
                httpMethod: 'OPTIONS',
                headers: {
                    'origin': testOrigin,
                    'access-control-request-method': 'GET',
                    'access-control-request-headers': 'X-Public-Key'
                }
            } as unknown as APIGatewayProxyEvent;

            const response = await getOrganizationDetailsLambda(event);

            // Verify CORS headers exist and are correct
            expect(response.headers).toBeDefined();
            expect(response.headers!['Access-Control-Allow-Origin']).toBe(testOrigin);
            expect(response.headers!['Access-Control-Allow-Methods']).toContain('GET');
            expect(response.headers!['Access-Control-Allow-Headers']).toContain('X-Public-Key');
        }
    });

    it.skip('should handle requests without origin header', async () => {
        const testUser = await createTestUser({
            accountType: 'parent',
            parentAccount: 'ROOT'
        });
        await seedTestUser(testUser);

        const event = {
            headers: {
                'X-Public-Key': testUser.publicKey
            },
            httpMethod: 'GET'
        } as unknown as APIGatewayProxyEvent;

        const response = await getOrganizationDetailsLambda(event);

        // Verify CORS headers exist and allow all origins
        expect(response.headers).toBeDefined();
        expect(response.headers!['Access-Control-Allow-Origin']).toBe('*');
        expect(response.headers!['Access-Control-Allow-Credentials']).toBe('true');
    });
}); 