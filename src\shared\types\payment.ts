export interface PaymentHistory {
  paymentId: string;
  userID: string;
  publicKey: string;
  amount: number;
  status: PaymentStatus;
  createdAt: number;
  completedAt?: number;
  previousBalance: number;
  newBalance: number;
  transactionId: string;
  stripePaymentIntentId?: string;
  invoiceEmail?: string;
}

export enum PaymentStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export interface PaymentResponse {
  payments: PaymentHistory[];
  summary: {
    totalPayments: number;
    totalAmount: number;
  };
  lastEvaluatedKey?: string;
}

export interface PaymentConfirmationEmailData {
  username: string;
  organizationName: string;
  amount: number;
  newBalance: number;
  transactionId: string;
  paymentDate: string;
  invoicePdf?: Buffer;
  invoiceEmail?: string;
}
