{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug Sign In Lambda Locally", "program": "${workspaceFolder}\\dist\\signInLambda\\tests\\localTest.js", "args": [], "sourceMaps": true, "outFiles": ["${workspaceFolder}\\dist\\**\\*.js"], "runtimeExecutable": "C:\\Program Files\\nodejs\\node.exe"}, {"type": "node", "request": "launch", "name": "Debug Sign Up Lambda Locally", "program": "${workspaceFolder}\\dist\\signUpLambda\\tests\\localTest.js", "args": [], "sourceMaps": true, "outFiles": ["${workspaceFolder}\\dist\\**\\*.js"], "runtimeExecutable": "C:\\Program Files\\nodejs\\node.exe"}, {"type": "node", "request": "launch", "name": "Debug Email Confirmation Lambda Locally", "program": "${workspaceFolder}\\dist\\emailConfirmationLambda\\tests\\localTest.js", "args": [], "sourceMaps": true, "outFiles": ["${workspaceFolder}\\dist\\**\\*.js"], "runtimeExecutable": "C:\\Program Files\\nodejs\\node.exe"}, {"type": "node", "request": "launch", "name": "Debug Resend Confirmation Email Lambda Locally", "program": "${workspaceFolder}\\dist\\resendConfirmationEmailLambda\\tests\\localTest.js", "args": [], "sourceMaps": true, "outFiles": ["${workspaceFolder}\\dist\\**\\*.js"], "runtimeExecutable": "C:\\Program Files\\nodejs\\node.exe"}]}