import { APIGatewayProxyEvent, Context, Callback, APIGatewayProxyResult } from 'aws-lambda';
import { emailConfirmationLambda } from '../handler';
import { 
  createMockEvent, 
  createMockContext,
  createTestUser,
  expectErrorResponse,
  expectSuccessResponse
} from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { getTestUser } from '../../shared/testUtils/databaseTestUtils';
import { initializeTestDatabase } from '../../shared/testUtils/databaseLifecycle';

describe('Email Confirmation Lambda Handler', () => {
  const mockContext = createMockContext();
  
  beforeAll(async () => {
    process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
    await initializeTestDatabase();
  });

  afterAll(async () => {
    delete process.env.USER_DETAILS_TABLE_NAME;
  });

  beforeEach(async () => {
    jest.clearAllMocks();
  });

  it.skip('should return error for expired token', async () => {
    const testUser = await createTestUser({
      emailVerified: false,
      verificationToken: 'ABC123XYZ789',
      verificationTokenExpiry: Date.now() - (2 * 365 * 24 * 60 * 60 * 1000) // 2 years ago
    });
    
    await seedTestUser(testUser);

    const event = createMockEvent({}, '/confirm-email');
    event.queryStringParameters = {
      token: testUser.verificationToken 
    };

    const response = await emailConfirmationLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'TOKEN_EXPIRED', 'Verification token has expired');
  });

  it.skip('should return error for missing token', async () => {
    const event = createMockEvent({}, '/confirm-email');
    event.queryStringParameters = {};

    const response = await emailConfirmationLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_TOKEN', 'Invalid verification token');
  });

  it.skip('should return error for non-existent user', async () => {
    const event = createMockEvent({}, '/confirm-email');
    event.queryStringParameters = {
      token: 'XYZ987DEF654'
    };
    console.log('[Test] Making request with token:', event.queryStringParameters.token);

    const response = await emailConfirmationLambda(event as APIGatewayProxyEvent);
    console.log('[Test] Received response:', JSON.stringify(response, null, 2));
    
    expectErrorResponse(response, 404, 'USER_NOT_FOUND', 'User not found');
  });

  it.skip('should return error for invalid token format', async () => {
    const event = createMockEvent({}, '/confirm-email');
    event.queryStringParameters = {
      token: '123456' // Invalid format (too short)
    };

    const response = await emailConfirmationLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(
      response, 
      400, 
      'INVALID_TOKEN_FORMAT',
      'Verification token must be 12 characters long and contain only uppercase letters and numbers'
    );
  });

  it.skip('should increment email confirmation count on successful verification', async () => {
    const testUser = await createTestUser({
      emailVerified: false,
      verificationToken: 'ABC123XYZ789',
      verificationTokenExpiry: Date.now() + 3600000
    });
    await seedTestUser(testUser);

    const event = createMockEvent({}, '/confirm-email');
    event.queryStringParameters = {
      token: 'ABC123XYZ789'
    };

    const response = await emailConfirmationLambda(event as APIGatewayProxyEvent);
    expectSuccessResponse(response, 200, {
      message: 'Email verified successfully'
    });
    
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.emailConfirmationCount).toBe(1);
  });

  it.skip('should increment counter even on failed confirmation', async () => {
    const testUser = await createTestUser({
      emailVerified: false,
      verificationToken: 'ABC123XYZ789',
      verificationTokenExpiry: Date.now() - 3600000 // expired token
    });
    await seedTestUser(testUser);

    const event = createMockEvent({}, '/confirm-email');
    event.queryStringParameters = {
      token: 'ABC123XYZ789'
    };

    const response = await emailConfirmationLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'TOKEN_EXPIRED', 'Verification token has expired');
    
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.emailConfirmationCount).toBe(1);
  });
});

