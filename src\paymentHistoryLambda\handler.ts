import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { getPaymentHistoryBypublicKey } from '../shared/database/paymentOperations';
import { ErrorResponse, SuccessResponse } from '../shared/responseUtils';
import { verifyToken } from '../shared/utils/tokenUtils';
import { PaymentResponse } from '../shared/types/payment';
import { getConfig } from '../shared/services/configService';

export const paymentHistoryLambda = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    // Load configuration from the service
    const config = await getConfig(event);
    
    console.log('[PAYMENT-HISTORY] Lambda triggered', {
      path: event.path,
      method: event.httpMethod,
      environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
      paymentTable: config.PAYMENT_HISTORY_TABLE_NAME
    });

    // Authorization Check
    const authHeader = event.headers.Authorization || event.headers.authorization;
    if (!authHeader) {
      return ErrorResponse(401, 'UNAUTHORIZED', 'Missing authorization token');
    }

    
    const token = authHeader.replace('Bearer ', '');
    const decodedToken = await verifyToken(token);
    if (!decodedToken || !decodedToken.accountType || decodedToken.accountType !== 'parent') {
      return ErrorResponse(401, 'UNAUTHORIZED', 'Only parent accounts can access payment history');
    }

    const { startDate, endDate, limit, startKey } = event.queryStringParameters || {};

    
    // Validate limit
    const limitNum = limit ? parseInt(limit) : 10;
    if (limit && isNaN(limitNum)) {
      return ErrorResponse(400, 'INVALID_LIMIT', 'Limit must be a valid number');
    }

    // Validate dates if provided
    let parsedStartDate: number | undefined;
    let parsedEndDate: number | undefined;
    
    if (startDate) {
      parsedStartDate = parseInt(startDate);
      if (isNaN(parsedStartDate)) {
        return ErrorResponse(400, 'INVALID_DATE_FORMAT', `Start date '${startDate}' must be a valid timestamp`);
      }
    }

    if (endDate) {
      parsedEndDate = parseInt(endDate);
      if (isNaN(parsedEndDate)) {
        return ErrorResponse(400, 'INVALID_DATE_FORMAT', `End date '${endDate}' must be a valid timestamp`);
      }
    }
    
    
    // Check if start date is after end date
    if (parsedStartDate && parsedEndDate && parsedStartDate > parsedEndDate) {
      return ErrorResponse(400, 'INVALID_DATE_RANGE', 'Start date cannot be after end date');
    }

    // Get payment history
    const paymentHistory = await getPaymentHistoryBypublicKey(
      decodedToken.publicKey || '',
      parsedStartDate,
      parsedEndDate,
      limitNum,
      startKey ? JSON.parse(startKey) : undefined,
      config.PAYMENT_HISTORY_TABLE_NAME
    );

    // Calculate summary
    const totalPayments = paymentHistory.payments.length;
    const totalAmount = paymentHistory.payments.reduce((sum, payment) => sum + payment.amount, 0);

    const response: PaymentResponse = {
      payments: paymentHistory.payments,
      summary: {
        totalPayments,
        totalAmount
      },
      lastEvaluatedKey: paymentHistory.lastEvaluatedKey ? 
        JSON.stringify(paymentHistory.lastEvaluatedKey) : undefined
    };

    return SuccessResponse(200, response);
  } catch (error) {
    console.error('Error fetching payment history:', error);
    return ErrorResponse(500, 'INTERNAL_ERROR', 'An error occurred while fetching payment history');
  }
};
 
 
