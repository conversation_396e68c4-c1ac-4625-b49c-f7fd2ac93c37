import { escape } from 'html-escaper';
import * as crypto from 'crypto';

export const sanitizeInput = (input: any): any => {
  if (typeof input === 'string') {
    return input.trim();
  } else if (typeof input === 'object' && input !== null) {
    return Object.keys(input).reduce((acc: any, key) => {
      acc[key] = sanitizeInput(input[key]);
      return acc;
    }, {});
  }
  return input;
};

export function sanitizeUserInput(input: Record<string, unknown>): Record<string, unknown> {
  const sanitizedInput: Record<string, unknown> = {};
  for (const [key, value] of Object.entries(input)) {
    if (typeof value === 'string') {
      sanitizedInput[key] = sanitizeInput(value);
    } else if (typeof value === 'object' && value !== null) {
      sanitizedInput[key] = sanitizeUserInput(value as Record<string, unknown>);
    } else {
      sanitizedInput[key] = value;
    }
  }
  return sanitizedInput;
}

export function generateOTP(length: number = 6): string {
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  return crypto.randomInt(min, max).toString().padStart(length, '0');
}
