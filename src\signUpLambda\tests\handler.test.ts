import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { signUpLambda } from '../handler';
import { generateOTP } from '../../shared/otp';
import { sendVerificationEmail } from '../../shared/emailService';
import { 
  createMockEvent, 
  createTestUser,
  expectErrorResponse,
  expectSuccessResponse,
  VALID_TEST_PASSWORD
} from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { getTestUser } from '../../shared/testUtils/databaseTestUtils';
import { getParentAccountBalances } from '../../shared/database/balanceOperations';

jest.mock('../../shared/emailService');
jest.mock('../../shared/otp');

describe('SignUp Lambda Handler', () => {
  let originalConsoleLog: any;

  beforeEach(async () => {
    console.log('Clearing test data...');
    await clearTestData();
    console.log('Test data cleared');
    originalConsoleLog = console.log;
    console.log = jest.fn();
    jest.clearAllMocks();
  });

  afterEach(() => {
    console.log = originalConsoleLog;
  });

  it.skip('should create a parent account successfully', async () => {
    const timestamp = Date.now();
    const uniqueEmail = `parent${timestamp}@example.com`;
    
    const event = createMockEvent({
        username: 'parentuser',
        email: uniqueEmail,
        password: VALID_TEST_PASSWORD
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    const body = JSON.parse(response.body);
    const userID = body.data.userID;
    const createdUser = await getTestUser(userID);
    expectSuccessResponse(response, 200, {
        data: {
            userID: expect.any(String),
            username: 'parentuser',
            email: uniqueEmail,
            createdAt: expect.any(Number),
            publicKey: expect.any(String),
            emailVerified: false,
            parentAccount: 'ROOT'
        }
    });
  });

  it.skip('should create a child account successfully with valid parentpublicKey', async () => {
    const timestamp = Date.now();
    const parentEmail = `parent${timestamp}@example.com`;
    const childEmail = `child${timestamp}@example.com`;

    const parentUser = await createTestUser({
        username: 'parentuser',
        email: parentEmail,
        parentAccount: 'ROOT',
        accountType: 'parent'
    });
    await seedTestUser(parentUser);

    const event = createMockEvent({
        username: 'childuser',
        email: childEmail,
        password: VALID_TEST_PASSWORD,
        parentpublicKey: parentUser.publicKey
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    const body = JSON.parse(response.body);
    const userID = body.data.userID;
    const createdUser = await getTestUser(userID);
    expectSuccessResponse(response, 200, {
        data: {
            userID: expect.any(String),
            username: 'childuser',
            email: childEmail,
            createdAt: expect.any(Number),
            publicKey: expect.any(String),
            emailVerified: false,
            parentAccount: parentUser.publicKey
        }
    });
  });

  it.skip('should return an error if parentpublicKey is invalid', async () => {
    const event = createMockEvent({
        username: 'childuser',
        email: '<EMAIL>',
        password: VALID_TEST_PASSWORD,
        parentpublicKey: 'INVALID_API_KEY'
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    expectErrorResponse(response, 400, 'INVALID_PARENT_PUBLIC_KEY_FORMAT', 'Invalid public key format');
  });

  it.skip('should return an error if parent account does not exist', async () => {
    const event = createMockEvent({
        username: 'childuser',
        email: '<EMAIL>',
        password: VALID_TEST_PASSWORD,
        parentpublicKey: 'APK_1234567890abcdef1234567890abcdef_1234567890'
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    expectErrorResponse(response, 404, 'PARENT_ACCOUNT_NOT_FOUND', 'Parent account does not exist.');
  });

  it.skip('should return error for missing required fields', async () => {
    const event = createMockEvent({
      username: 'test_user'
      // missing email and password
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    expectErrorResponse(response, 400, 'MISSING_REQUIRED_FIELDS', 'Email and password are required');
  });

  it.skip('should return error for invalid username format', async () => {
    const event = createMockEvent({
      username: 'test*user',
      email: '<EMAIL>',
      password: 'Test@Password123'
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    expectErrorResponse(response, 400, 'INVALID_USERNAME_FORMAT', 'Username can only contain letters, numbers, and underscores');
  });

  it.skip('should return error for invalid email format', async () => {
    const event = createMockEvent({
      username: 'testuser',
      email: 'invalid-email',
      password: 'Test@Password123'
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    expectErrorResponse(response, 400, 'INVALID_EMAIL_FORMAT', 'Email address is not in a valid format');
  });

  it.skip('should return error for weak password', async () => {
    const event = createMockEvent({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'weak'
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    expectErrorResponse(response, 400, 'INVALID_PASSWORD_FORMAT', 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');
  });

  it.skip('should return error for username exceeding maximum length', async () => {
    await clearTestData();
    const timestamp = Date.now();
    const uniqueEmail = `test${timestamp}@example.com`;
    
    const event = createMockEvent({
      username: 'a'.repeat(31),  // 31 characters (exceeds 30 char limit)
      email: uniqueEmail,
      password: VALID_TEST_PASSWORD
    }, '/signup');
  
    const response = await signUpLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    expectErrorResponse(response, 400, 'INVALID_USERNAME_FORMAT', 
      'Username must not exceed 30 characters');
  });

  it.skip('should generate unique public keys for different users', async () => {
    const user1 = await createTestUser();
    const user2 = await createTestUser();
    
    await seedTestUser(user1);
    await seedTestUser(user2);
    
    expect(user1.publicKey).not.toBe(user2.publicKey);
    expect(user1.publicKey).toMatch(/^APK_[a-f0-9]{32}_\d+$/);
    expect(user2.publicKey).toMatch(/^APK_[a-f0-9]{32}_\d+$/);
  });

  it.skip('should return an error if parent account has duplicate username', async () => {
    // First create a parent account
    const firstParent = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(firstParent);

    // Try to create another parent account with same username
    const event = createMockEvent({
      username: 'parentuser',
      email: '<EMAIL>',
      password: VALID_TEST_PASSWORD
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 409, 'USERNAME_ALREADY_TAKEN', 
      'Username is already taken by another parent account');
  });

  it.skip('should return an error if parent account has duplicate email', async () => {
    const timestamp = Date.now();
    const duplicateEmail = `parent${timestamp}@example.com`;
    
    // First create a parent account
    const firstParent = await createTestUser({
      username: 'firstparent',
      email: duplicateEmail,
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(firstParent);

    // Try to create another parent account with same email
    const event = createMockEvent({
      username: 'secondparent',
      email: duplicateEmail,
      password: VALID_TEST_PASSWORD
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 409, 'EMAIL_ALREADY_REGISTERED', 
      'Email is already registered as a parent account');
  });

  it.skip('should return an error if a user tries to self-parent with same email', async () => {
    const parentUser = await createTestUser({
        username: 'parentuser',
        email: '<EMAIL>',
        parentAccount: 'ROOT'
    });
    await seedTestUser(parentUser);

    const event = createMockEvent({
        username: 'differentuser',
        email: '<EMAIL>', // Same email, different username
        password: VALID_TEST_PASSWORD,
        parentpublicKey: parentUser.publicKey
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_PARENT_ACCOUNT', 
        'You cannot create a child account with the same email or username as your parent account.');
  });

  it.skip('should return an error if a user tries to self-parent with same username', async () => {
    const parentUser = await createTestUser({
        username: 'parentuser',
        email: '<EMAIL>',
        parentAccount: 'ROOT'
    });
    await seedTestUser(parentUser);

    const event = createMockEvent({
        username: 'parentuser', // Same username, different email
        email: '<EMAIL>',
        password: VALID_TEST_PASSWORD,
        parentpublicKey: parentUser.publicKey
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_PARENT_ACCOUNT', 
        'You cannot create a child account with the same email or username as your parent account.');
  });

  it.skip('should allow creating parent account when user has only child account', async () => {
    // First create a parent user
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(parentUser);

    // Create a child user
    const childUser = await createTestUser({
      username: 'childuser',
      email: '<EMAIL>',
      parentAccount: parentUser.publicKey,
      accountType: 'child'
    });
    await seedTestUser(childUser);

    // Try to create a parent account with same email/username as child
    const event = createMockEvent({
      username: 'childuser',
      email: '<EMAIL>',
      password: VALID_TEST_PASSWORD
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent);
    expectSuccessResponse(response, 200, {
      data: {
        userID: expect.any(String),
        username: 'childuser',
        email: '<EMAIL>',
        createdAt: expect.any(Number),
        publicKey: expect.any(String),
        emailVerified: false,
        parentAccount: 'ROOT'
      }
    });
  });

  it.skip('should return an error if child account has duplicate email under same parent', async () => {
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(parentUser);

    // Create first child
    const firstChild = await createTestUser({
      username: 'childuser1',
      email: '<EMAIL>',
      parentAccount: parentUser.publicKey,
      accountType: 'child'
    });
    await seedTestUser(firstChild);

    // Try to create second child with same email
    const event = createMockEvent({
      username: 'childuser2',
      email: '<EMAIL>',  // Same email as first child
      password: VALID_TEST_PASSWORD,
      parentpublicKey: parentUser.publicKey
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 409, 'DUPLICATE_EMAIL', 
      'An account with this email already exists under this parent account');
  });

  it.skip('should allow same email under different parents', async () => {
    // Create first parent
    const parentUser1 = await createTestUser({
      username: 'parent1',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(parentUser1);

    // Create second parent
    const parentUser2 = await createTestUser({
      username: 'parent2',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(parentUser2);

    // Create child under first parent
    const childUser1 = await createTestUser({
      username: 'child1',
      email: '<EMAIL>',
      parentAccount: parentUser1.publicKey,
      accountType: 'child'
    });
    await seedTestUser(childUser1);

    // Try to create child with same email under second parent
    const event = createMockEvent({
      username: 'child2',
      email: '<EMAIL>',
      password: VALID_TEST_PASSWORD,
      parentpublicKey: parentUser2.publicKey
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent);
    expectSuccessResponse(response, 200, {
      data: expect.objectContaining({
        email: '<EMAIL>',
        parentAccount: parentUser2.publicKey
      })
    });
  });

  it.skip('should allow child accounts with same username but different email under same parent', async () => {
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(parentUser);

    // Create first child
    const firstChild = await createTestUser({
      username: 'childuser',
      email: '<EMAIL>',
      parentAccount: parentUser.publicKey,
      accountType: 'child'
    });
    await seedTestUser(firstChild);

    // Create second child with same username but different email
    const event = createMockEvent({
      username: 'childuser',
      email: '<EMAIL>',
      password: VALID_TEST_PASSWORD,
      parentpublicKey: parentUser.publicKey
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent);
    expectSuccessResponse(response, 200, {
      data: expect.objectContaining({
        username: 'childuser',
        email: '<EMAIL>',
        parentAccount: parentUser.publicKey
      })
    });
  });

  it.skip('should send verification email with parent organization name for child accounts', async () => {
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent',
      organizationName: 'PAK TRADER'
    });
    await seedTestUser(parentUser);

    const event = createMockEvent({
      username: 'childuser',
      email: '<EMAIL>',
      password: VALID_TEST_PASSWORD,
      parentpublicKey: parentUser.publicKey
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent);
    
    expect(sendVerificationEmail).toHaveBeenCalledWith(
      '<EMAIL>',
      expect.any(String),
      'child',
      parentUser.publicKey
    );
  });

  it.skip('should send verification email with parent auth URLs for child accounts', async () => {
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent',
      organizationName: 'PAK TRADER',
      authUrls: {
        verify: 'https://www.paktrader.com/confirm-email',
        reset: 'https://www.paktrader.com/reset-password',
        update: 'https://www.paktrader.com/update-password',
        signin: 'https://www.paktrader.com/sign-in',
        signup: 'https://www.paktrader.com/sign-up',
        successful: 'https://www.paktrader.com/success',
        resend: 'https://www.paktrader.com/resend-verification'
      }
    });
    await seedTestUser(parentUser);

    const event = createMockEvent({
      username: 'childuser',
      email: '<EMAIL>',
      password: VALID_TEST_PASSWORD,
      parentpublicKey: parentUser.publicKey
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent);
    
    expect(sendVerificationEmail).toHaveBeenCalledWith(
      '<EMAIL>',
      expect.any(String),
      'child',
      parentUser.publicKey
    );

    // Get the token from the mock call
    const mockToken = (sendVerificationEmail as jest.Mock).mock.calls[0][1];
    expect(mockToken).toBeDefined();

    // Check if any of the console.log calls contain our verification URL
    const consoleLogCalls = (console.log as jest.Mock).mock.calls;
    const verificationUrlLog = consoleLogCalls.find(call => 
      call[0] === 'Attempting to send verification email...' ||
      call[0] === 'Verification email sent successfully'
    );
    expect(verificationUrlLog).toBeDefined();
  });

  it.skip('should create a parent account with initial balance', async () => {
    console.log('Starting balance test');
    const timestamp = Date.now();
    const uniqueEmail = `parent${timestamp}@example.com`;
    
    console.log('Creating mock event');
    const event = createMockEvent({
      username: 'parentuser',
      email: uniqueEmail,
      password: VALID_TEST_PASSWORD
    }, '/signup');

    console.log('Calling signUpLambda');
    const response = await signUpLambda(event as APIGatewayProxyEvent) as APIGatewayProxyResult;
    console.log('signUpLambda response received:', response.statusCode);
    console.log('Response body:', response.body);
    
    // Verify the response has the expected structure
    expectSuccessResponse(response, 200, {
      data: {
        userID: expect.any(String),
        username: 'parentuser',
        email: uniqueEmail,
        createdAt: expect.any(Number),
        publicKey: expect.any(String),
        emailVerified: false,
        parentAccount: 'ROOT'
      }
    });
    
    // Extract the userID from the response
    const body = JSON.parse(response.body);
    console.log('Parsed response body:', body);
    
    // Handle both possible response structures
    const userData = body.data.data || body.data;
    if (!userData || !userData.userID) {
      console.error('Missing userID in response:', body);
      throw new Error('Missing userID in response');
    }
    
    const userID = userData.userID;
    console.log(`User ID extracted: ${userID}`);
    
    // Use getParentAccountBalances instead of getTestUser
    console.log('Getting parent account balances');
    const balances = await getParentAccountBalances(userID);
    console.log('Balances retrieved:', balances);
    
    // Verify the balance fields
    expect(balances.accountBalance).toBe(3.00);
    expect(balances.availableBalance).toBe(3.00);
    console.log('Test completed');
  });

  it.skip('should handle parallel operations during signup', async () => {
    const timestamp = Date.now();
    const email = `test${timestamp}@example.com`;
    const username = `testuser${timestamp}`; // Make username unique
    
    const startTime = Date.now();
    
    const event = createMockEvent({
      username,  // Use unique username
      email,
      password: VALID_TEST_PASSWORD
    }, '/signup');

    const response = await signUpLambda(event as APIGatewayProxyEvent);
    const duration = Date.now() - startTime;

    // Verify response
    expectSuccessResponse(response, 200, {
      data: expect.objectContaining({
        username,
        email
      })
    });

    // Verify parallel operations completed faster
    expect(duration).toBeLessThan(3000);
  });
  
  it.skip('should handle early validation failures efficiently', async () => {
    const startTime = Date.now();
    
    const event = createMockEvent({
      username: 'test@invalid',  // Invalid username format
      email: '<EMAIL>',
      password: VALID_TEST_PASSWORD
    }, '/signup');
  
    const response = await signUpLambda(event as APIGatewayProxyEvent);
    const duration = Date.now() - startTime;
  
    // Verify quick failure
    expect(duration).toBeLessThan(1000); // Should fail fast
    expectErrorResponse(response, 400, 'INVALID_USERNAME_FORMAT', 
      'Username can only contain letters, numbers, and underscores');
  
    // Verify no database operations were attempted
    expect(sendVerificationEmail).not.toHaveBeenCalled();
  });
});
