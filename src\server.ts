import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import AWS from 'aws-sdk';
import { signUpLambda } from './signUpLambda/handler';
import { signInLambda } from './signInLambda/handler';
import { emailConfirmationLambda } from './emailConfirmationLambda/handler';
import { resendConfirmationEmailLambda } from './resendConfirmationEmailLambda/handler';
import { resetPasswordLambda } from './resetPasswordLambda/handler';
import { updatePasswordLambda } from './updatePasswordLambda/handler';
import { APIGatewayProxyEvent } from 'aws-lambda';
import { Environment } from './shared/services/environment';
import { corsOptions } from './shared/config/cors.config';
import { updateOrganizationLambda } from './updateOrganizationLambda/handler';
import { getOrganizationDetailsLambda } from './getOrganizationDetailsLambda/handler';
import { getChildAccountsLambda } from './getChildAccountsLambda/handler';
import { costCalculatorLambda } from './costCalculatorLambda/handler';
// import { updateOrganizationLambda } from './updateOrganizationLambda/handler';
// Add this with other imports
import { billingHistoryLambda } from './billingHistoryLambda/handler';
import { stripeWebhookLambda } from './stripeWebhookLambda/handler';


// Add this with other routes

// Load environment variables
dotenv.config();

// Remove or comment out these lines
// process.env.NODE_ENV = 'production';
// process.env.IS_LOCAL = 'false';
console.log('Server starting in production mode with environment:', {
  NODE_ENV: process.env.NODE_ENV,
  IS_LOCAL: process.env.IS_LOCAL,
  FRONTEND_URL: process.env.FRONTEND_URL
});

console.log('Loaded Environment Variables:', {
  SMTP_HOST: process.env.SMTP_HOST,
  SMTP_PORT: process.env.SMTP_PORT,
  SMTP_USER: process.env.SMTP_USER,
  SECRET_NAME: process.env.SECRET_NAME,
  REGION: process.env.REGION
});

// Configure AWS SDK
AWS.config.update({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!
  }
});

const app = express();

// Add CORS configuration at the top of your Express app setup
app.use(cors(corsOptions));
app.options('*', cors(corsOptions)); // Enable pre-flight for all routes

// app.use(express.json());

// Middleware to capture raw body
app.use(
  '/webhooks/stripe',
  express.raw({ type: 'application/json' }) // Capture raw body for Stripe
);

const PORT = 3000;

// Middleware to create Lambda event
const createLambdaEvent = (req: express.Request): APIGatewayProxyEvent => {
  return {
    body: JSON.stringify(req.body || {}),
    headers: req.headers as { [key: string]: string },
    path: req.path,
    httpMethod: req.method,
    isBase64Encoded: false,
    queryStringParameters: req.query as { [key: string]: string } || null,  // Fix here
    multiValueQueryStringParameters: null,
    pathParameters: null,
    stageVariables: null,
    requestContext: null as any,
    resource: '',
    multiValueHeaders: {}
  };
};

app.post('/auth/signin', async (req, res) => {
  try {
    const result = await signInLambda(createLambdaEvent(req));
    res.status(result.statusCode).json(JSON.parse(result.body));
  } catch (error) {
    console.error('Signin Error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

app.get('/auth/confirm-email', cors(corsOptions), async (req, res) => {
  try {
    const lambdaEvent = createLambdaEvent(req);
    const result = await emailConfirmationLambda(lambdaEvent);
    
    if (result.statusCode === 200) {
      return res.redirect(`${process.env.FRONTEND_URL}/email-verified`);
    }
    
    res.redirect(`${process.env.FRONTEND_URL}/error?message=${encodeURIComponent(result.body)}`);
  } catch (error) {
    console.error('Email Confirmation Error:', error);
    res.redirect(`${process.env.FRONTEND_URL}/error?message=Internal Server Error`);
  }
});

app.post('/auth/request-new-confirmation', async (req, res) => {
  try {
    const result = await resendConfirmationEmailLambda(createLambdaEvent(req));
    console.log('Request New Confirmation Response:', result);
    res.status(result.statusCode).json(JSON.parse(result.body));
  } catch (error) {
    console.error('Request New Confirmation Error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

app.post('/auth/signup', async (req, res) => {
  try {
    const result = await signUpLambda(createLambdaEvent(req));
    console.log('Signup Lambda Response:', result);
    
    const parsedBody = JSON.parse(result.body);
    console.log('Parsed Signup Response:', parsedBody);
    
    res.status(result.statusCode).json(parsedBody);
  } catch (error) {
    console.error('Signup Error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

app.post('/auth/reset-password', async (req, res) => {
  try {
    const result = await resetPasswordLambda(createLambdaEvent(req));
    console.log('Reset Password Request Response:', result);
    res.status(result.statusCode).json(JSON.parse(result.body));
  } catch (error) {
    console.error('Reset Password Request Error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

app.post('/auth/update-password', async (req, res) => {
  try {
    const result = await updatePasswordLambda(createLambdaEvent(req));
    console.log('Update Password Response: ', result);
    res.status(result.statusCode).json(JSON.parse(result.body));
  } catch (error) {
    console.error('Update Password Error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

app.post('/auth/update-organization', async (req, res) => {
  try {
    const result = await updateOrganizationLambda(createLambdaEvent(req));
    console.log('Update Organization Response:', result);
    res.status(result.statusCode).json(JSON.parse(result.body));
  } catch (error) {
    console.error('Update Organization Error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

app.get('/auth/organization-details', cors(corsOptions), async (req, res) => {
    console.log('Express CORS Debug:', {
        origin: req.headers.origin,
        method: req.method,
        headers: {
            'access-control-request-method': req.headers['access-control-request-method'],
            'access-control-request-headers': req.headers['access-control-request-headers']
        }
    });
    // Log the request origin for debugging
    console.log('Request Origin:', req.headers.origin);
    
    try {
        const result = await getOrganizationDetailsLambda(createLambdaEvent(req));
        
        // Ensure CORS headers are set
        res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
        res.header('Access-Control-Allow-Credentials', 'true');
        
        res.status(result.statusCode).json(JSON.parse(result.body));
    } catch (error) {
        console.error('Get Organization Details Error:', error);
        res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error occurred'
        });
    }
});

app.get('/auth/child-accounts', async (req, res) => {
    try {
        const result = await getChildAccountsLambda(createLambdaEvent(req));
        res.status(result.statusCode).json(JSON.parse(result.body));
    } catch (error) {
        console.error('Get Child Accounts Error:', error);
        res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error occurred'
        });
    }
});

app.get('/parent/cost-analysis', async (req, res) => {
    try {
        const result = await costCalculatorLambda(createLambdaEvent(req));
        res.status(result.statusCode).json(JSON.parse(result.body));
    } catch (error) {
        console.error('Cost Analysis Error:', error);
        res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Add with other routes, after costCalculatorLambda route
app.get('/parent/billing-history', async (req, res) => {
    try {
        const lambdaEvent = createLambdaEvent(req);
        console.log('Billing History Request:', {
            headers: req.headers,
            query: req.query
        });
        const result = await billingHistoryLambda(lambdaEvent);
        res.status(result.statusCode).json(JSON.parse(result.body));
    } catch (error) {
        console.error('Billing History Error:', error);
        res.status(500).json({
            error: 'Internal Server Error',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});

// Add new route after other routes
app.post('/webhooks/stripe', async (req, res) => {
  try {
    const rawBody = req.body;
    const signature = req.headers['stripe-signature'];
    
    console.log('Debug webhook payload:', {
      isBuffer: Buffer.isBuffer(rawBody),
      rawBodyType: typeof rawBody,
      rawBodyLength: rawBody ? rawBody.length : 0
    });
    
    // Create Lambda event with the raw buffer
    const lambdaEvent: APIGatewayProxyEvent = {
      body: rawBody,
      headers: {
        'stripe-signature': signature as string,
        'content-type': 'application/json'
      },
      httpMethod: 'POST',
      path: '/webhooks/stripe',
      isBase64Encoded: false,
      queryStringParameters: null,
      multiValueQueryStringParameters: null,
      pathParameters: null,
      stageVariables: null,
      requestContext: null as any,
      resource: '',
      multiValueHeaders: {}
    };
    
    const result = await stripeWebhookLambda(lambdaEvent);
    res.status(result.statusCode).send(result.body);
  } catch (error) {
    console.error('Stripe Webhook Error:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : null,
      rawBodyType: typeof req.body,
      isBuffer: Buffer.isBuffer(req.body)
    });
    res.status(400).json({
      error: 'Webhook Error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});
app.listen(PORT, () => {
  console.log(`API Server running locally at http://localhost:${PORT}`);
  console.log(`Frontend URL configured as: ${Environment.getBaseUrl()}`);
});

