import { APIGatewayProxyEvent } from 'aws-lambda';
import { DynamoDB } from 'aws-sdk';
import { paymentHistoryLambda } from '../handler';
import { verifyToken } from '../../shared/utils/tokenUtils';
import { 
    createMockEvent, 
    createTestUser,
    expectErrorResponse,
    expectSuccessResponse
} from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, initializeTestDatabase, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { PaymentStatus } from '../../shared/types/payment';

const dynamoDB = new DynamoDB.DocumentClient();
jest.mock('../../shared/utils/tokenUtils');

describe('Payment History Lambda Handler', () => {
    beforeAll(async () => {
        process.env.PAYMENT_HISTORY_TABLE_NAME = 'paymentHistory';
        process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
        process.env.ENCRYPTION_KEY = "dBG9kF7bH5cJ2mN8pQ4sT6wX0yZ3vA1eI/nL+uM9oR4=";
        process.env.JWT_SECRET = "test-secret-key";
        await initializeTestDatabase();
    });

    afterAll(() => {
        delete process.env.PAYMENT_HISTORY_TABLE_NAME;
        delete process.env.USER_DETAILS_TABLE_NAME;
        delete process.env.ENCRYPTION_KEY;
        delete process.env.JWT_SECRET;
    });

    const setupTestUser = async (username: string) => {
        const user = await createTestUser({
            username,
            email: `${username}@example.com`,
            accountType: 'parent',
            parentAccount: 'ROOT'
        });
        await seedTestUser(user);
        
        (verifyToken as jest.Mock).mockReturnValue({
            userID: user.userID,
            email: user.email,
            accountType: 'parent',
            publicKey: user.publicKey
        });
        
        return user;
    };

    const createEventWithAuth = (path: string, queryParams = {}) => {
        const event = createMockEvent({}, path);
        event.headers = {
            ...event.headers,
            Authorization: 'Bearer valid-token'
        };
        if (Object.keys(queryParams).length > 0) {
            event.queryStringParameters = queryParams;
        }
        return event;
    };

    it.skip('should retrieve payment history successfully', async () => {
        const parentUser = await setupTestUser('parentuser');
        
        // Create test payment records
        const testPayments = [
            {
                paymentId: 'payment1',
                userID: parentUser.userID,
                publicKey: parentUser.publicKey,
                amount: 100,
                status: PaymentStatus.COMPLETED,
                createdAt: Date.now(),
                completedAt: Date.now() + 1000,
                previousBalance: 0,
                newBalance: 100,
                transactionId: 'tx1'
            },
            {
                paymentId: 'payment2',
                userID: parentUser.userID,
                publicKey: parentUser.publicKey,
                amount: 50,
                status: PaymentStatus.COMPLETED,
                createdAt: Date.now() - ********, // 1 day ago
                completedAt: Date.now() - 86300000,
                previousBalance: 100,
                newBalance: 150,
                transactionId: 'tx2'
            }
        ];

        // Seed the payments using DocumentClient
        await Promise.all(testPayments.map(payment => 
            dynamoDB.put({
                TableName: process.env.PAYMENT_HISTORY_TABLE_NAME!,
                Item: payment
            }).promise()
        ));

        const event = createEventWithAuth('/parent/payment-history');
        const response = await paymentHistoryLambda(event as APIGatewayProxyEvent);

        expectSuccessResponse(response, 200, {
            payments: expect.arrayContaining([
                expect.objectContaining({
                    amount: 100,
                    status: PaymentStatus.COMPLETED
                }),
                expect.objectContaining({
                    amount: 50,
                    status: PaymentStatus.COMPLETED
                })
            ]),
            summary: {
                totalPayments: 2,
                totalAmount: 150
            }
        });
    });

    it.skip('should reject requests without authorization header', async () => {
        const event = createMockEvent({}, '/parent/payment-history');
        const response = await paymentHistoryLambda(event as APIGatewayProxyEvent);
        expectErrorResponse(response, 401, 'UNAUTHORIZED', 'Missing authorization token');
    });

    it.skip('should reject non-parent accounts', async () => {
        (verifyToken as jest.Mock).mockReturnValue({
            userID: 'child-user-id',
            email: '<EMAIL>',
            accountType: 'child',
            publicKey: 'child-public-key'
        });

        const event = createEventWithAuth('/parent/payment-history');
        const response = await paymentHistoryLambda(event as APIGatewayProxyEvent);
        expectErrorResponse(response, 401, 'UNAUTHORIZED', 'Only parent accounts can access payment history');
    });

    it.skip('should filter payment history by date range', async () => {
        const parentUser = await setupTestUser('daterangeuser');
        const now = Date.now();
        
        const testPayments = [
            {
                paymentId: 'payment1',
                userID: parentUser.userID,
                publicKey: parentUser.publicKey,
                amount: 100,
                status: PaymentStatus.COMPLETED,
                createdAt: now - 5000, // 5 seconds ago
                completedAt: now - 4000, // 4 seconds ago
                previousBalance: 0,
                newBalance: 100,
                transactionId: 'tx1'
            },
            {
                paymentId: 'payment2',
                userID: parentUser.userID,
                publicKey: parentUser.publicKey,
                amount: 50,
                status: PaymentStatus.COMPLETED,
                createdAt: now - ********, // 1 day ago
                completedAt: now - 86300000, // 1 day ago + 1000
                previousBalance: 100,
                newBalance: 150,
                transactionId: 'tx2'
            }
        ];

        console.log('Seeding test payments:', testPayments);
        await Promise.all(testPayments.map(payment => 
            dynamoDB.put({
                TableName: process.env.PAYMENT_HISTORY_TABLE_NAME!,
                Item: payment
            }).promise()
        ));

        const event = createEventWithAuth('/parent/payment-history', {
            startDate: (now - ********).toString(), // 1 day ago
            endDate: now.toString()
        });

        console.log('Event for payment history retrieval:', event);
        const response = await paymentHistoryLambda(event as APIGatewayProxyEvent);
        console.log('Response from payment history lambda:', response);

        expectSuccessResponse(response, 200, {
            payments: expect.arrayContaining([
                expect.objectContaining({ amount: 100 }),
                expect.objectContaining({ amount: 50 })
            ]),
            summary: {
                totalPayments: 2,
                totalAmount: 150
            }
        });
    });

    it.skip('should handle pagination correctly', async () => {
        const parentUser = await setupTestUser('paginationuser');
        const payments = Array.from({ length: 15 }, (_, i) => ({
            paymentId: `payment${i}`,
            userID: parentUser.userID,
            publicKey: parentUser.publicKey,
            amount: 100,
            status: PaymentStatus.COMPLETED,
            createdAt: Date.now() - i * 3600000, // 1 hour apart
            completedAt: Date.now() - i * 3600000 + 1000,
            previousBalance: i * 100,
            newBalance: (i + 1) * 100,
            transactionId: `tx${i}`
        }));
    
        console.log('Seeding pagination test payments:', payments);
        await Promise.all(payments.map(payment => 
            dynamoDB.put({
                TableName: process.env.PAYMENT_HISTORY_TABLE_NAME!,
                Item: payment
            }).promise()
        ));
    
        const firstPageEvent = createEventWithAuth('/parent/payment-history', { limit: '10' });
        const firstResponse = await paymentHistoryLambda(firstPageEvent as APIGatewayProxyEvent);
        const firstPageData = JSON.parse(firstResponse.body);
        
        console.log('First page data:', firstPageData);
        expect(firstPageData.data.payments.length).toBe(10);
        expect(firstPageData.data.lastEvaluatedKey).toBeTruthy();
    
        // Log the last evaluated key
        console.log('Last Evaluated Key for second page:', firstPageData.data.lastEvaluatedKey);
    
        const secondPageEvent = createEventWithAuth('/parent/payment-history', {
            limit: '10',
            startKey: Buffer.from(JSON.stringify(firstPageData.data.lastEvaluatedKey)).toString('base64') // Base64 encode the key
        });
        
        const secondResponse = await paymentHistoryLambda(secondPageEvent as APIGatewayProxyEvent);
        const secondPageData = JSON.parse(secondResponse.body);
    
        console.log('Second page data:', secondPageData);
        
        // Check if the response is successful before accessing properties
        if (secondPageData.success) {
            expect(secondPageData.data.payments.length).toBe(5); // Adjust based on your expected results
            expect(secondPageData.data.lastEvaluatedKey).toBeUndefined(); // Adjust based on your expected results
        } else {
            console.error('Error in second page response:', secondPageData.error);
            throw new Error('Failed to fetch second page of payments');
        }
    });

    it.skip('should handle empty payment history', async () => {
        const parentUser = await setupTestUser('emptyuser');
        const event = createEventWithAuth('/parent/payment-history');
        const response = await paymentHistoryLambda(event as APIGatewayProxyEvent);

        expectSuccessResponse(response, 200, {
            payments: [],
            summary: {
                totalPayments: 0,
                totalAmount: 0
            }
        });
    });
});
