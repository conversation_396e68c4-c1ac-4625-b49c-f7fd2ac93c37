import { APIGatewayProxyEvent, Context, Callback, APIGatewayProxyResult } from 'aws-lambda';
import { resendConfirmationEmailLambda } from '../handler';
import { generateVerificationToken } from '../../shared/verification/tokenGenerator';
import { sendVerificationEmail } from '../../shared/emailService';
import { 
  createMockEvent, 
  createMockContext,
  createTestUser,
  expectErrorResponse,
  expectSuccessResponse
} from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { getTestUser } from '../../shared/testUtils/databaseTestUtils';
import { VERIFICATION_RESEND_COOLDOWN } from '../../shared/constants';
import { initializeTestDatabase } from '../../shared/testUtils/databaseLifecycle';

jest.mock('../../shared/emailService');
jest.mock('../../shared/verification/tokenGenerator');

describe('Resend Confirmation Email Lambda Handler', () => {
  const mockContext = createMockContext();
  const callback: Callback<APIGatewayProxyResult> = (error, result) => {};
  
  beforeAll(async () => {
    process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
    await initializeTestDatabase();
  });

  afterAll(async () => {
    delete process.env.USER_DETAILS_TABLE_NAME;
  });

  beforeEach(async () => {
    await clearTestData();
    jest.clearAllMocks();
    (sendVerificationEmail as jest.Mock).mockResolvedValue(undefined);
  });

  it.skip('should resend verification link successfully', async () => {
    const testUser = await createTestUser({
      emailVerified: false,
      verificationToken: 'ABC123XYZ789',
      verificationTokenExpiry: Date.now() - 3600000, // Expired token
      lastVerificationTokenSentAt: Date.now() - (VERIFICATION_RESEND_COOLDOWN + 1000) // Past the cooldown period
    });
    
    await seedTestUser(testUser);
    const mockToken = 'DEF456UVW123';
    (generateVerificationToken as jest.Mock).mockReturnValue(mockToken);

    const event = createMockEvent({
      email: testUser.email
    }, '/auth/request-new-confirmation');

    const response = await resendConfirmationEmailLambda(
      event as APIGatewayProxyEvent
    );

    expectSuccessResponse(response, 200, {
      message: 'A new verification link has been sent to your email'
    });

    expect(sendVerificationEmail).toHaveBeenCalledWith(testUser.email, mockToken);
    
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser).toMatchObject({
      verificationToken: mockToken,
      verificationTokenExpiry: expect.any(Number),
      lastVerificationSentAt: expect.any(Number)
    });
  });

  it.skip('should return error for rate limit exceeded', async () => {
    const testUser = await createTestUser({
      emailVerified: false,
      verificationToken: 'ABC123XYZ789',
      verificationTokenExpiry: Date.now() + 3600000,
      lastVerificationTokenSentAt: Date.now() // Just sent
    });
    
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email
    }, '/auth/request-new-confirmation');

    const response = await resendConfirmationEmailLambda(event as APIGatewayProxyEvent);
    
    expectErrorResponse(
      response, 
      429, 
      'RATE_LIMIT_EXCEEDED',
      'Please wait 60 seconds before requesting a new verification link'
    );

    // Verify rate limit headers
    expect(response.headers).toMatchObject({
      'X-RateLimit-Limit': '1',
      'X-RateLimit-Remaining': '0',
      'X-RateLimit-Reset': expect.any(Number)
    });
  });

  it.skip('should return error for already verified email', async () => {
    const testUser = await createTestUser({
      emailVerified: true
    });
    
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email
    }, '/auth/request-new-confirmation');

    const response = await resendConfirmationEmailLambda(
      event as APIGatewayProxyEvent
    );

    expectErrorResponse(response, 400, 'EMAIL_ALREADY_VERIFIED', 'Email is already verified');
  });

  it.skip('should return error for non-existent email', async () => {
    const event = createMockEvent({
      email: '<EMAIL>'
    }, '/auth/request-new-confirmation');

    const response = await resendConfirmationEmailLambda(
      event as APIGatewayProxyEvent
    );

    expectErrorResponse(response, 404, 'USER_NOT_FOUND', 'User not found');
  });

  it.skip('should resend verification email with parent organization name for child accounts', async () => {
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent',
      organizationName: 'Test Organization'
    });
    await seedTestUser(parentUser);

    const childUser = await createTestUser({
      username: 'childuser',
      email: '<EMAIL>',
      parentAccount: parentUser.publicKey,
      accountType: 'child',
      emailVerified: false,
      verificationToken: 'ABC123XYZ789',
      verificationTokenExpiry: Date.now() - 3600000, // Expired token
      lastVerificationTokenSentAt: Date.now() - (VERIFICATION_RESEND_COOLDOWN + 1000) // Past the cooldown period
    });
    await seedTestUser(childUser);

    const mockToken = 'DEF456UVW123';
    (generateVerificationToken as jest.Mock).mockReturnValue(mockToken);

    const event = createMockEvent({
      email: childUser.email
    }, '/resend-confirmation');

    const response = await resendConfirmationEmailLambda(event as APIGatewayProxyEvent);

    expect(sendVerificationEmail).toHaveBeenCalledWith(
      childUser.email,
      mockToken,
      'child',
      parentUser.publicKey
    );
  });
});

describe('Resend Email Counter Tests', () => {
  beforeAll(async () => {
    process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
    await initializeTestDatabase();
  });

  afterAll(async () => {
    delete process.env.USER_DETAILS_TABLE_NAME;
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    (sendVerificationEmail as jest.Mock).mockResolvedValue(undefined);
  });

  it.skip('should increment count on successful resend', async () => {
    const mockToken = 'ABC123XYZ789';
    (generateVerificationToken as jest.Mock).mockReturnValue(mockToken);
    
    const testUser = await createTestUser({
      emailVerified: false,
      verificationToken: 'ABC123XYZ789',
      verificationTokenExpiry: Date.now() - 3600000, // Expired token
      lastVerificationTokenSentAt: Date.now() - (VERIFICATION_RESEND_COOLDOWN + 1000) // Past cooldown
    });
    
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email
    }, '/auth/request-new-confirmation');

    await resendConfirmationEmailLambda(event as APIGatewayProxyEvent);
    
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.resendEmailCount).toBe(1);
    expect(updatedUser?.verificationToken).toBe(mockToken);
    expect(sendVerificationEmail).toHaveBeenCalledWith(
      testUser.email, 
      mockToken,
      testUser.accountType,
      testUser.parentAccount
    );
  });

  it.skip('should increment count even when rate limited', async () => {
    const testUser = await createTestUser({
      emailVerified: false,
      verificationToken: 'ABC123XYZ789',
      verificationTokenExpiry: Date.now() + 3600000,
      lastVerificationTokenSentAt: Date.now() // Just sent
    });
    
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email
    }, '/auth/request-new-confirmation');

    await resendConfirmationEmailLambda(event as APIGatewayProxyEvent);
    
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.resendEmailCount).toBe(1);
  });

  it.skip('should increment count when email is already verified', async () => {
    const testUser = await createTestUser({
      emailVerified: true
    });
    
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email
    }, '/auth/request-new-confirmation');

    await resendConfirmationEmailLambda(event as APIGatewayProxyEvent);
    
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.resendEmailCount).toBe(1);
  });
});
