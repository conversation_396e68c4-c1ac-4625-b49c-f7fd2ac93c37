// Database Configuration
export const DB_CONFIG = {
  TABLE_NAME: 'userAuthentication',
  INDEXES: {
    EMAIL: 'emailIndex',
    USERNAME: 'usernameIndex',
    OTP: 'otpIndex',
    ACCOUNT_STATUS: 'accountStatusIndex'
  }
};
 
// JWT Configuration
export const JWT_CONFIG = {
  SECRET: process.env.JWT_SECRET || 'your-default-secret-key',
  EXPIRATION: process.env.JWT_EXPIRATION || '1h'
};

// Time Configuration (in milliseconds)
export const TIME_CONFIG = {
  OTP_EXPIRY: 900000,        // 15 minutes
  OTP_COOLDOWN: 300000,      // 5 minutes
  LOGIN_LOCKOUT: 900000      // 15 minutes
};

// API Configuration
export const API_CONFIG = {
  BASE_URL: 'https://api.authiqa.com',
  ENDPOINTS: {
    SIGNUP: '/auth/signup',
    SIGNIN: '/auth/signin',
    CONFIRM_EMAIL: '/auth/confirm-email',
    RESEND_CONFIRMATION: '/auth/request-new-confirmation'
  }
};

 


