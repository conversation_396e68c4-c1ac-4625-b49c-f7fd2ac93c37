import { APIGatewayProxyEvent } from 'aws-lambda';
import { getOrganizationDetailsLambda } from '../handler';
import { 
    createMockEvent,
    createTestUser,
    expectErrorResponse,
    expectSuccessResponse
} from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { getTestUser } from '../../shared/testUtils/databaseTestUtils';

describe('Get Organization Details Lambda Handler', () => {
    beforeEach(async () => {
        await clearTestData();
        jest.clearAllMocks();
    });

    it.skip('should return auth URLs for parent account', async () => {
        const parentUser = await createTestUser({
            username: 'parentuser',
            email: '<EMAIL>',
            accountType: 'parent',
            parentAccount: 'ROOT',
            authUrls: {
                signin: 'https://test.com/signin',
                signup: 'https://test.com/signup',
                verify: 'https://test.com/verify',
                reset: 'https://test.com/reset',
                update: 'https://test.com/update',
                resend: 'https://test.com/resend',
                successful: 'https://test.com/success'
            }
        });
        await seedTestUser(parentUser);

        const event = createMockEvent({}, '/auth/organization-details');
        event.headers = { 'X-Public-Key': parentUser.publicKey };

        const response = await getOrganizationDetailsLambda(event as APIGatewayProxyEvent);
        expectSuccessResponse(response, 200, {
            message: 'Organization details retrieved successfully',
            authUrls: parentUser.authUrls
        });
    });

    it.skip('should return error for invalid Public key', async () => {
        const event = createMockEvent({}, '/auth/organization-details');
        event.headers = { 'X-Public-Key': 'invalid-public-key' };

        const response = await getOrganizationDetailsLambda(event as APIGatewayProxyEvent);
        expectErrorResponse(response, 404, 'USER_NOT_FOUND', 'User not found');
    });

    it.skip('should return error when Public key is missing', async () => {
        const event = createMockEvent({}, '/auth/organization-details');
        
        const response = await getOrganizationDetailsLambda(event as APIGatewayProxyEvent);
        expectErrorResponse(response, 400, 'MISSING_PARENT_Public_KEY', 'Public key is required');
    });

    it.skip('should increment organization details retrieval count', async () => {
        const parentUser = await createTestUser({
            username: 'parentuser',
            email: '<EMAIL>',
            accountType: 'parent',
            parentAccount: 'ROOT',
            organizationDetailsRetrievalCount: 0,
            authUrls: {
                signin: 'https://test.com/signin',
                signup: 'https://test.com/signup',
                verify: 'https://test.com/verify',
                reset: 'https://test.com/reset',
                update: 'https://test.com/update',
                resend: 'https://test.com/resend',
                successful: 'https://test.com/success'
            }
        });
        await seedTestUser(parentUser);

        const event = createMockEvent({}, '/auth/organization-details');
        event.headers = { 'X-Public-Key': parentUser.publicKey };

        const response = await getOrganizationDetailsLambda(event as APIGatewayProxyEvent);
        expectSuccessResponse(response, 200, {
            message: 'Organization details retrieved successfully',
            authUrls: parentUser.authUrls
        });

        const updatedUser = await getTestUser(parentUser.userID);
        expect(updatedUser?.organizationDetailsRetrievalCount).toBe(1);
    });

    it.skip('should handle database errors when incrementing retrieval count', async () => {
        const parentUser = await createTestUser({
            accountType: 'parent',
            parentAccount: 'ROOT',
            authUrls: {
                signin: 'https://test.com/signin',
                signup: 'https://test.com/signup',
                verify: 'https://test.com/verify',
                reset: 'https://test.com/reset',
                update: 'https://test.com/update',
                resend: 'https://test.com/resend',
                successful: 'https://test.com/success'
            }
        });
        await seedTestUser(parentUser);

        // Mock database error for increment but allow the main operation to succeed
        jest.spyOn(require('../../shared/database/userOperations'), 'incrementOrganizationDetailsRetrievalCount')
            .mockRejectedValueOnce(new Error('Database error'));

        const event = createMockEvent({}, '/auth/organization-details');
        event.headers = { 'X-Public-Key': parentUser.publicKey };

        const response = await getOrganizationDetailsLambda(event as APIGatewayProxyEvent);
        
        // Should still return success even if counter increment fails
        expectSuccessResponse(response, 200, {
            message: 'Organization details retrieved successfully',
            authUrls: parentUser.authUrls
        });
    });
}); 