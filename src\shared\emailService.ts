import * as nodemailer from 'nodemailer';
import { Environment } from './services/environment';
import { getUserByPublicKey } from './database/userOperations';
import { PaymentConfirmationEmailData } from '../shared/types/payment';
import { APIGatewayProxyEvent } from 'aws-lambda';
import { getConfig } from '../shared/services/configService';

const createTransporter = async (event?: APIGatewayProxyEvent) => {
  const config = await Environment.getEmailConfig(event);
 
  return nodemailer.createTransport({
    host: config.host,
    port: config.port,
    secure: true,
    auth: {
      user: config.user,
      pass: config.password
    }
  });
};

interface EmailSenderInfo {
  senderName: string;
  senderEmail: string;
}

async function getEmailSender(accountType: string, parentPublicKey?: string, tableName?: string): Promise<EmailSenderInfo> {
  // Ensure we have a valid email address
  const senderEmail = process.env.SMTP_USER || '<EMAIL>';
  
  // Make sure the email is properly formatted
  const formattedEmail = senderEmail.includes('@') ? senderEmail : '<EMAIL>';
  
  if (accountType === 'child' && parentPublicKey) {
    try {
      console.log(`Getting email sender for parent with key ${parentPublicKey} in table ${tableName || 'default'}`);
      const parentUser = await getUserByPublicKey(parentPublicKey, tableName);
      return {
        senderName: parentUser?.organizationName || 'Authiqa',
        senderEmail: formattedEmail
      };
    } catch (error) {
      console.error('Error getting parent user:', error);
      // Fallback to default
      return {
        senderName: 'Authiqa',
        senderEmail: formattedEmail
      };
    }
  }
  
  return {
    senderName: 'Authiqa',
    senderEmail: formattedEmail
  };
}

export const sendVerificationEmail = async (
  toEmail: string, 
  token: string,
  accountType?: string,
  parentPublicKey?: string,
  verifyAuthPath?: string,
  event?: APIGatewayProxyEvent
) => {
  // Get config if event is provided to determine the correct table name
  let tableName: string | undefined;
  if (event) {
    try {
      const config = await getConfig(event);
      tableName = config.USER_DETAILS_TABLE_NAME;
      console.log(`Using table name from config: ${tableName}`);
    } catch (error) {
      console.error('Error getting table name from config:', error);
    }
  }

  if (Environment.isLocal()) {
    console.log('Local Development: Email verification skipped here', {
      to: toEmail,
      token: token,
      message: 'Email would have been sent in production',
      sharedModuleVersion: '1.0.1'
    });
    return;
  }

  const transporter = await createTransporter(event);

  console.log('Transporter created:', transporter);

  const sender = await getEmailSender(accountType || 'parent', parentPublicKey, tableName);
  console.log('Email sender information:', {
    senderName: sender.senderName,
    senderEmail: sender.senderEmail,
    formattedFrom: `"${sender.senderName}" <${sender.senderEmail}>`
  });
  
  const verificationLink = await (async () => {
    // If it's a child account, get parent's auth URLs
    if (accountType === 'child' && parentPublicKey) {
      // Pass the table name from config
      console.log(`Looking up parent user with key ${parentPublicKey} in table ${tableName || 'default'}`);
      const parentUser = await getUserByPublicKey(parentPublicKey, tableName);
      
      if (!parentUser) {
        throw new Error(`Parent user not found in table ${tableName || 'default'}`);
      }
      
      if (verifyAuthPath) {
        // Don't concatenate full URLs - check if verifyAuthPath is a full URL
        if (verifyAuthPath.startsWith('http')) {
          return `${verifyAuthPath}?token=${token}`;
        }
        // Otherwise, properly join the paths
        const baseUrl = parentUser.organizationUrl?.replace(/\/+$/, '');
        const cleanPath = verifyAuthPath.startsWith('/') ? verifyAuthPath : `/${verifyAuthPath}`;
        return `${baseUrl}${cleanPath}?token=${token}`;
      }
    }
    
    // Get frontend URL from config service if event is provided
    let baseUrl = 'https://authiqa.com';
    if (event) {
      try {
        const config = await getConfig(event);
        baseUrl = config.FRONTEND_URL;
        console.log(`Using frontend URL from config service: ${baseUrl}`);
      } catch (error) {
        console.error('Error getting frontend URL from config service:', error);
      }
    }
    
    // Fallback to environment variable if config service failed
    if (!baseUrl || baseUrl === 'https://authiqa.com') {
      baseUrl = process.env.FRONTEND_URL || 'https://authiqa.com';
    }
    
    const cleanBaseUrl = baseUrl.replace(/\/+$/, '');
    return `${cleanBaseUrl}/confirm-email?token=${token}`;
  })();

  console.log('Generated verification link:', verificationLink);
  
  const mailOptions = {
    from: `"${sender.senderName}" <${sender.senderEmail}>`,
    to: toEmail,
    subject: `Email Verification From ${sender.senderName}`,
    html: `
      <div style="background-color: #f6f8fa; padding: 20px 0;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
          <div style="padding: 20px 25px;">
            <h1 style="color: #24292e; font-size: 24px; font-weight: 500; margin: 0 0 20px;">Email Verification</h1>
            <p style="color: #444d56; font-size: 16px; line-height: 1.5;">Please click the button below to verify your email address:</p>
            <div style="margin: 30px 0; text-align: center;">
              <a href="${verificationLink}" style="display: inline-block; padding: 12px 24px; font-size: 16px; font-weight: 500; line-height: 1; text-decoration: none; background-color: #2ea44f; color: #ffffff; border-radius: 6px;">Verify Email</a>
            </div>
            <p style="color: #444d56; font-size: 14px; line-height: 1.5;">This verification link will expire in 15 minutes.</p>
            <p style="color: #444d56; font-size: 14px; line-height: 1.5;">If you didn't create an account with us, you can safely ignore this email.</p>
            <p style="color: #444d56; font-size: 14px; line-height: 1.5;">For security reasons, this link can only be used once.</p>
          </div>
          <div style="border-top: 1px solid #e1e4e8; padding: 15px 25px;">
            <p style="color: #6a737d; font-size: 12px; line-height: 1.5; margin: 0;">Sent by ${sender.senderName}</p>
          </div>
        </div>
      </div>
    `
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log('Verification email sent successfully to:', toEmail);
  } catch (error) {
    console.error('Error sending verification email:', error);
    throw error;
  }
};

// We'll implement this later for welcome emails using SES
export const sendWelcomeEmail = async (
  toEmail: string,
  event?: APIGatewayProxyEvent
) => {
  // TODO: Implement SES welcome email
  console.log('Welcome email functionality coming soon');
};

export const sendPasswordResetEmail = async (
  toEmail: string, 
  resetLink: string,
  accountType?: string,
  parentPublicKey?: string,
  updatePasswordPath?: string,
  event?: APIGatewayProxyEvent
) => {
  // Get config if event is provided to determine the correct table name
  let tableName: string | undefined;
  if (event) {
    try {
      const config = await getConfig(event);
      tableName = config.USER_DETAILS_TABLE_NAME;
      console.log(`Using table name from config for password reset: ${tableName}`);
    } catch (error) {
      console.error('Error getting table name from config for password reset:', error);
    }
  }

  if (Environment.isLocal()) {
    console.log('Local Development: Password reset email skipped');
    return;
  }

  console.log('Sending password reset email with:', {
    toEmail,
    accountType,
    hasParentPublicKey: !!parentPublicKey,
    updatePasswordPath,
    resetLink,
    tableName
  });

  const transporter = await createTransporter(event);
  const sender = await getEmailSender(accountType || 'parent', parentPublicKey, tableName);

  const mailOptions = {
    from: `"${sender.senderName}" <${sender.senderEmail}>`,
    to: toEmail,
    subject: `Password Reset From ${sender.senderName}`,
    html: `
      <div style="background-color: #f6f8fa; padding: 20px 0;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
          <div style="padding: 20px 25px;">
            <h1 style="color: #24292e; font-size: 24px; font-weight: 500; margin: 0 0 20px;">Reset Your Password</h1>
            <p style="color: #444d56; font-size: 16px; line-height: 1.5;">Please click the button below to reset your password:</p>
            <div style="margin: 30px 0; text-align: center;">
              <a href="${resetLink}" style="display: inline-block; padding: 12px 24px; font-size: 16px; font-weight: 500; line-height: 1; text-decoration: none; background-color: #2ea44f; color: #ffffff; border-radius: 6px;">Reset Password</a>
            </div>
            <p style="color: #444d56; font-size: 14px; line-height: 1.5;">This link will expire in 15 minutes.</p>
            <p style="color: #444d56; font-size: 14px; line-height: 1.5;">If you didn't request this reset, please ignore this email.</p>
            <p style="color: #444d56; font-size: 14px; line-height: 1.5;">For security reasons, this link can only be used once.</p>
          </div>
          <div style="border-top: 1px solid #e1e4e8; padding: 15px 25px;">
            <p style="color: #6a737d; font-size: 12px; line-height: 1.5; margin: 0;">Sent by ${sender.senderName}</p>
          </div>
        </div>
      </div>
    `
  };

  await transporter.sendMail(mailOptions);
  console.log('Password reset email sent successfully to:', toEmail);
}

// New function for low balance notification
export interface LowBalanceEmailData {
  username: string;
  accountBalance: number;
  availableBalance: number;
  usagePercentage: number;
  alertType: 'warning' | 'critical' | 'depleted';
}

export const sendLowBalanceEmail = async (
  toEmail: string,
  data: LowBalanceEmailData,
  accountType: string = 'parent',
  event?: APIGatewayProxyEvent
) => {
  // Get config if event is provided to determine the correct table name
  let tableName: string | undefined;
  if (event) {
    try {
      const config = await getConfig(event);
      tableName = config.USER_DETAILS_TABLE_NAME;
      console.log(`Using table name from config for low balance email: ${tableName}`);
    } catch (error) {
      console.error('Error getting table name from config for low balance email:', error);
    }
  }

  if (Environment.isLocal()) {
    console.log('Local Development: Low balance email skipped', {
      to: toEmail,
      data,
      message: 'Email would have been sent in production'
    });
    return;
  }

  console.log(`Sending low balance alert (${data.alertType}) to ${toEmail}`);
  
  const transporter = await createTransporter(event);
  const sender = await getEmailSender(accountType, undefined, tableName);

  // Determine alert color and message based on alert type
  let alertColor = '#FFA500'; // Orange for warning
  let alertTitle = 'Low Balance Alert';
  let alertMessage = `Your account has used ${data.usagePercentage}% of your available balance.`;
  
  if (data.alertType === 'critical') {
    alertColor = '#FF4500'; // Red-orange for critical
    alertTitle = 'Critical Balance Alert';
    alertMessage = `Your account has used ${data.usagePercentage}% of your available balance. Please add funds soon to avoid service interruption.`;
  } else if (data.alertType === 'depleted') {
    alertColor = '#FF0000'; // Red for depleted
    alertTitle = 'Account Balance Depleted';
    alertMessage = 'Your account balance has been fully utilized. Please add funds to continue using our services.';
  }

  // Get payment link from config if available
  let paymentLink = 'https://authiqa.com/signin?action=payment';
  if (event) {
    try {
      const config = await getConfig(event);
      const baseUrl = config.FRONTEND_URL || 'https://authiqa.com';
      paymentLink = `${baseUrl.replace(/\/+$/, '')}/signin?action=payment`;
      console.log(`Using payment link from config service: ${paymentLink}`);
    } catch (error) {
      console.error('Error getting frontend URL from config service:', error);
    }
  } else {
    console.log('No event provided, using default payment link');
  }

  const mailOptions = {
    from: `"${sender.senderName}" <${sender.senderEmail}>`,
    to: toEmail,
    subject: `${alertTitle} - Authiqa`,
    html: `
      <div style="background-color: #f6f8fa; padding: 20px 0;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
          <div style="padding: 20px 25px;">
            <h1 style="color: ${alertColor}; font-size: 24px; font-weight: 500; margin: 0 0 20px;">${alertTitle}</h1>
            
            <p style="color: #444d56; font-size: 16px; line-height: 1.5;">Hello ${data.username},</p>
            
            <p style="color: #444d56; font-size: 16px; line-height: 1.5;">${alertMessage}</p>
            
            <div style="background-color: #f6f8fa; border-radius: 6px; padding: 15px; margin: 20px 0;">
              <p style="color: #24292e; font-size: 16px; margin: 0 0 10px; font-weight: 500;">Account Balance Summary:</p>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; color: #444d56;">Total Account Balance:</td>
                  <td style="padding: 8px 0; color: #24292e; text-align: right;">$${data.accountBalance.toFixed(2)}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; color: #444d56;">Available Balance:</td>
                  <td style="padding: 8px 0; color: ${data.availableBalance < 1 ? '#FF0000' : '#24292e'}; text-align: right;">$${data.availableBalance.toFixed(2)}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; color: #444d56;">Usage Percentage:</td>
                  <td style="padding: 8px 0; color: ${alertColor}; text-align: right;">${data.usagePercentage}%</td>
                </tr>
              </table>
            </div>
            
            <p style="color: #444d56; font-size: 16px; line-height: 1.5;">To add funds to your account, please visit our payment page on Authiqa:</p>
            
            <div style="margin: 30px 0; text-align: center;">
              <a href="${paymentLink}" style="background-color: #0366d6; color: #ffffff; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Add Funds</a>
            </div>
            
            <p style="color: #444d56; font-size: 14px; line-height: 1.5;">If you have any questions about your account or billing, please contact our support team.</p>
          </div>
          <div style="border-top: 1px solid #e1e4e8; padding: 15px 25px;">
            <p style="color: #6a737d; font-size: 12px; line-height: 1.5; margin: 0;">Sent by ${sender.senderName}</p>
          </div>
        </div>
      </div>
    `
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log(`Low balance email (${data.alertType}) sent successfully to:`, toEmail);
    return true;
  } catch (error) {
    console.error(`Error sending low balance email (${data.alertType}):`, error);
    throw error;
  }
};

export const sendPaymentConfirmationEmail = async (
  toEmail: string,
  data: PaymentConfirmationEmailData,
  accountType: string = 'parent',
  status: 'success' | 'failed' = 'success',
  event?: APIGatewayProxyEvent
) => {
  // Get config if event is provided to determine the correct table name
  let tableName: string | undefined;
  if (event) {
    try {
      const config = await getConfig(event);
      tableName = config.USER_DETAILS_TABLE_NAME;
      console.log(`Using table name from config for payment confirmation: ${tableName}`);
    } catch (error) {
      console.error('Error getting table name from config for payment confirmation:', error);
    }
  }

  if (Environment.isLocal()) {
    console.log('Local Development: Payment confirmation email skipped', {
      to: toEmail,
      data,
      message: 'Email would have been sent in production'
    });
    return;
  }

  console.log(`Sending payment ${status} email to ${toEmail}`);
  
  const transporter = await createTransporter(event);
  const sender = await getEmailSender(accountType, undefined, tableName);

  // Determine styling based on status
  const statusConfig = {
    success: {
      color: '#28a745',
      title: 'Payment Successful',
      message: 'Thank you for your payment. Attached is your invoice for your records.',
      support: ' If you have any questions, please contact our support team.'
    },
    failed: {
      color: '#dc3545',
      title: 'Payment Failed',
      message: 'We were unable to process your payment. ',
      support: 'If you have any questions, please contact our support team.'
    }
  };

  const config = statusConfig[status];

  // Get support link from config if available
  let supportLink = 'https://authiqa.com/support';
  if (event) {
    try {
      const config = await getConfig(event);
      const baseUrl = config.FRONTEND_URL || 'https://authiqa.com';
      supportLink = `${baseUrl.replace(/\/+$/, '')}/support`;
      console.log(`Using support link from config service: ${supportLink}`);
    } catch (error) {
      console.error('Error getting frontend URL from config service:', error);
    }
  } else {
    console.log('No event provided, using default support link');
  }

  const mailOptions = {
    from: `"${sender.senderName}" <${sender.senderEmail}>`,
    to: toEmail,
    subject: `Payment ${status === 'success' ? 'Confirmation' : 'Failed'} - Authiqa`,
    html: `
      <div style="background-color: #f6f8fa; padding: 20px 0;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
          <div style="padding: 20px 25px;">
            <h1 style="color: ${config.color}; font-size: 24px; font-weight: 500; margin: 0 0 20px;">${config.title}</h1>
            
            <p style="color: #444d56; font-size: 16px; line-height: 1.5;">Hello ${data.organizationName},</p>
            
            <p style="color: #444d56; font-size: 16px; line-height: 1.5;">${config.message}</p>
            
            <p style="color: #444d56; font-size: 14px; line-height: 1.5;">${config.support}</p>
          </div>
          <div style="border-top: 1px solid #e1e4e8; padding: 15px 25px;">
            <p style="color: #6a737d; font-size: 12px; line-height: 1.5; margin: 0;">Sent by ${sender.senderName}</p>
          </div>
        </div>
      </div>
    `,
    attachments: data.invoicePdf ? [
      {
        filename: `invoice-${data.transactionId}.pdf`,
        content: data.invoicePdf,
        contentType: 'application/pdf'
      }
    ] : undefined
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log('Payment confirmation email sent successfully to:', toEmail);
    return true;
  } catch (error) {
    console.error('Error sending payment confirmation email:', error);
    throw error;
  }
};


