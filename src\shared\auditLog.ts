import { PutCommand, QueryCommand } from "@aws-sdk/lib-dynamodb";
import { dynamoDB } from './database';
import { DB_CONFIG } from './config';

export async function logLoginAttempt(userID: string, email: string, success: boolean, ipAddress: string) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    userID,
    email,
    timestamp,
    success,
    ipAddress,
    attemptId: `${userID}_${timestamp}`
  };

  await dynamoDB.send(new PutCommand({
    TableName: `${DB_CONFIG.TABLE_NAME}_LoginAudit`,
    Item: logEntry
  }));
}

export async function getRecentLoginAttempts(userID: string, limit: number = 10) {
  const result = await dynamoDB.send(new QueryCommand({
    TableName: `${DB_CONFIG.TABLE_NAME}_LoginAudit`,
    KeyConditionExpression: 'userID = :userID',
    ExpressionAttributeValues: {
      ':userID': userID
    },
    Limit: limit,
    ScanIndexForward: false
  }));

  return result.Items;
}
