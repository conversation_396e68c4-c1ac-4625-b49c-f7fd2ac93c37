import { APIGatewayProxyResult } from 'aws-lambda';
import * as Yup from 'yup';
import { ErrorResponse } from './responseUtils';

const defaultHeaders = {
  'Content-Type': 'application/json',
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Credentials': true,
};

export class AppError extends Error {
  constructor(
    public code: string,
    public statusCode: number,
    message: string
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export const ErrorTypes = {
  // Authentication/Authorization Errors (4xx)
  INVALID_CREDENTIALS: () => ErrorResponse(401, 'INVALID_CREDENTIALS', 'Invalid email or password'),
  UNAUTHORIZED: (message: string) => ErrorResponse(401, 'UNAUTHORIZED', message),
  FORBIDDEN: (message: string) => ErrorResponse(403, 'FORBIDDEN', message),
  
  // Resource Errors (4xx)
  NOT_FOUND: (message: string) => ErrorResponse(404, 'NOT_FOUND', message),
  CONFLICT: (message: string) => ErrorResponse(409, 'CONFLICT', message),
  
  // Validation Errors (400)
  VALIDATION_ERROR: (message: string) => ErrorResponse(400, 'VALIDATION_ERROR', message),
  INVALID_REQUEST: (message: string) => ErrorResponse(400, 'INVALID_REQUEST', message),
  
  // Server Errors (5xx)
  INTERNAL: (message: string) => ErrorResponse(500, 'INTERNAL_SERVER_ERROR', message)
};

export function handleError(error: unknown): APIGatewayProxyResult {
  console.error('Error:', error);

  if (error instanceof AppError) {
    return {
      statusCode: error.statusCode,
      headers: defaultHeaders,
      body: JSON.stringify({
        status: 'error',
        code: error.code,
        message: error.message,
      }),
    };
  }

  if (error instanceof Yup.ValidationError) {
    return {
      statusCode: 400,
      headers: defaultHeaders,
      body: JSON.stringify({
        status: 'error',
        code: 'VALIDATION_ERROR',
        message: error.message,
      }),
    };
  }

  // Handle AWS SDK errors
  if (error && typeof error === 'object' && 'code' in error) {
    return {
      statusCode: 500,
      headers: defaultHeaders,
      body: JSON.stringify({
        status: 'error',
        code: 'AWS_ERROR',
        message: 'An AWS service error occurred',
      }),
    };
  }

  return {
    statusCode: 500,
    headers: defaultHeaders,
    body: JSON.stringify({
      status: 'error',
      code: 'INTERNAL_SERVER_ERROR',
      message: 'An unexpected error occurred',
    }),
  };
}

export function createSuccessResponse(data: any, statusCode: number = 200): APIGatewayProxyResult {
  return {
    statusCode,
    headers: defaultHeaders,
    body: JSON.stringify({
      status: 'success',
      data,
    }),
  };
}
