import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { getUserByVerificationToken, confirmUserEmail, incrementEmailConfirmationCount } from '../shared/database/userOperations';
import { validateTokenFormat } from '../shared/verification/tokenGenerator';
import { ErrorTypes } from '../shared/errorTypes';
import { addCorsHeaders } from '../shared/corsHandler';
import { ErrorResponse, SuccessResponse } from '../shared/responseUtils';
import { getConfig } from '../shared/services/configService';

export const emailConfirmationLambda = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  // Load configuration from the new service
  const config = await getConfig(event);
  
  console.log('[EMAIL-CONFIRMATION] Lambda invoked with headers:', {
    host: event.headers.Host || event.headers.host,
    origin: event.headers.Origin || event.headers.origin
  });
  
  console.log('[CONFIG] EmailConfirmationLambda using configuration service', {
    environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
    frontendUrl: config.FRONTEND_URL,
    userTable: config.USER_DETAILS_TABLE_NAME
  });
  
  console.log('Starting email confirmation process: ', {
    hasToken: !!event.queryStringParameters?.token,
    environment: config.NODE_ENV,
    apiVersion: '2.0'
  });
  
  try {
    const token = event.queryStringParameters?.token;

    if (!token) {
      console.log('Token missing, returning INVALID_TOKEN');
      return ErrorTypes.EMAIL_VERIFICATION.INVALID_TOKEN();
    }

    if (!validateTokenFormat(token)) {
      console.log('Invalid token format:', token);
      return ErrorTypes.EMAIL_VERIFICATION.INVALID_FORMAT();
    }

    const user = await getUserByVerificationToken(token, config.USER_DETAILS_TABLE_NAME);
    if (!user) {
      console.log('No user found, returning USER_NOT_FOUND');
      return ErrorTypes.USER_NOT_FOUND();
    }

    // Increment counter after finding valid user but before completing verification
    await incrementEmailConfirmationCount(user.userID, config.USER_DETAILS_TABLE_NAME);

    if (!user.verificationTokenExpiry || user.verificationTokenExpiry < Date.now()) {
      console.log('Token expired, returning TOKEN_EXPIRED');
      return ErrorTypes.EMAIL_VERIFICATION.TOKEN_EXPIRED();
    }

    await confirmUserEmail(user.userID, config.USER_DETAILS_TABLE_NAME);
    
    return addCorsHeaders(SuccessResponse(200, {
      message: 'Email verified successfully'
    }), event);

  } catch (error) {
    console.error('Error confirming email:', error);
    
    // Use config to determine if we should show detailed errors
    if (config.NODE_ENV === 'local') {
      const detailedMessage = error instanceof Error ? error.message : 'An internal server error occurred';
      return addCorsHeaders(ErrorResponse(500, 'INTERNAL_SERVER_ERROR', detailedMessage), event);
    } else {
      return addCorsHeaders(ErrorTypes.INTERNAL_ERROR(), event);
    }
  }
};
 

