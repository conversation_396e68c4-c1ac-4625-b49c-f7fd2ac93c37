import { signInLambda } from '../handler';
import { APIGatewayProxyEvent, Context, Callback, APIGatewayProxyResult } from 'aws-lambda';
import { createMockEvent, createMockContext, createTestUser, VALID_TEST_PASSWORD } from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, seedTestUser } from '../../shared/testUtils/databaseLifecycle';

async function runLocalTest() {

  console.log('Running local test for Sign In Lambda ');
  
  try {
    await clearTestData();
    
    const testUser = await createTestUser({
      emailVerified: true,
      accountStatus: 'active',
      accountType: 'parent',
      parentAccount: 'ROOT'
    });
    
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email,
      password: VALID_TEST_PASSWORD
    }, '/signin');
    
    const context = createMockContext();
    const callback: Callback<APIGatewayProxyResult> = (error, result) => {};


    const result = await signInLambda(event as APIGatewayProxyEvent);
    console.log('Result:', result);

    await clearTestData();
  } catch (error) {
    console.error('Error:', error);
  }
}

runLocalTest().catch(console.error);
