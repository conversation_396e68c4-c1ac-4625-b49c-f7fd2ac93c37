import { DynamoDB } from 'aws-sdk';

const dynamoDB = new DynamoDB.DocumentClient();
const TABLE_NAME = process.env.BILLING_TABLE_NAME || 'authiqaBilling';

export interface BillingRecord {
  publicKey: string;
  monthYear: string;  // Format: "2024-01"
  totalAccounts: number;
  costAssociatedWithAccounts: number;
  totalIOInteractions: number;
  costAssociatedWithIO: number;
  totalFinalCost: number;
  timestamp: number;
}

export const storeMonthlyBillingRecord = async (record: BillingRecord, tableName?: string): Promise<void> => {
  const params = {
    TableName: tableName || TABLE_NAME,
    Item: record,
    // Ensure we don't overwrite existing records for the same month
    ConditionExpression: 'attribute_not_exists(monthYear) OR monthYear <> :monthYear',
    ExpressionAttributeValues: {
      ':monthYear': record.monthYear
    }
  };

  try {
    await dynamoDB.put(params).promise();
  } catch (error: any) {
    if (error.code === 'ConditionalCheckFailedException') {
      throw new Error(`Billing record already exists for ${record.monthYear}`);
    }
    throw error;
  }
};

export const getBillingHistory = async (
  publicKey: string,
  startDate?: string,
  endDate?: string,
  limit?: number,
  tableName?: string
): Promise<BillingRecord[]> => {
  const params: any = {
    TableName: tableName || TABLE_NAME,
    KeyConditionExpression: 'publicKey = :publicKey',
    ExpressionAttributeValues: {
      ':publicKey': publicKey
    },
    ScanIndexForward: false
  };

  if (startDate && endDate) {
    params.KeyConditionExpression += ' AND monthYear BETWEEN :startDate AND :endDate';
    params.ExpressionAttributeValues[':startDate'] = startDate;
    params.ExpressionAttributeValues[':endDate'] = endDate;
  } else if (startDate) {
    params.KeyConditionExpression += ' AND monthYear >= :startDate';
    params.ExpressionAttributeValues[':startDate'] = startDate;
  } else if (endDate) {
    params.KeyConditionExpression += ' AND monthYear <= :endDate';
    params.ExpressionAttributeValues[':endDate'] = endDate;
  }

  if (limit) {
    params.Limit = limit;
  }

  const result = await dynamoDB.query(params).promise();
  return result.Items as BillingRecord[];
};
