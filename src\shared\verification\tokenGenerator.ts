import crypto from 'crypto';

export const generateVerificationToken = (): string => {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let result = '';
  const bytes = crypto.randomBytes(12);
  
  for (let i = 0; i < 12; i++) {
    result += chars[bytes[i] % chars.length];
  }
  
  return result;
};

export const validateTokenFormat = (token: string): boolean => {
  return /^[0-9A-Z]{12}$/.test(token);
}; 