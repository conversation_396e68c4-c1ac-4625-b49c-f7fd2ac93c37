import { AuthUrls } from '../types';

interface ValidationResult {
    isValid: boolean;
    message: string;
}

export function 


validateOrganizationName(name: string): ValidationResult {
    if (!name || typeof name !== 'string') {
        return {
            isValid: false,
            message: 'Organization name is required'
        };
    }

    // Only check that the name isn't empty or just whitespace
    if (name.trim().length === 0) {
        return {
            isValid: false,
            message: 'Organization name cannot be empty'
        };
    }

    // Check for reasonable length (1-100 characters)
    if (name.length > 200) {
        return {
            isValid: false,
            message: 'Organization name is too long (maximum 200 characters)'
        };
    }

    return {
        isValid: true,
        message: ''
    };
}

export function validateOrganizationUrl(url: string): ValidationResult {
    if (!url || typeof url !== 'string') {
        return {
            isValid: false,
            message: 'Organization URL is required'
        };
    }
    try {
        const urlObj = new URL(url);
        if (urlObj.protocol !== 'http:' && urlObj.protocol !== 'https:') {
            return {
                isValid: false,
                message: 'Organization URL must start with http:// or https://'
            };
        }
        return {
            isValid: true,
            message: ''
        };
    } catch {
        return {
            isValid: false,
            message: 'Invalid URL format'
        };
    }
}

export function validateAuthUrls(authUrls: AuthUrls, organizationUrl: string): ValidationResult {
    if (!authUrls) {
        return {
            isValid: false,
            message: 'Authentication URLs are required'
        };
    }

    // Extract domain from organization URL
    const orgDomain = new URL(organizationUrl).hostname;

    // Required URL fields
    const requiredUrls = [
        'signup',
        'signin',
        'verify',
        'reset',
        'update',
        'resend',
        'successful'
    ];

    // Check if all required URLs are present
    for (const urlField of requiredUrls) {
        if (!authUrls[urlField as keyof AuthUrls]) {
            return {
                isValid: false,
                message: `${urlField} is required`
            };
        }

        try {
            const url = new URL(authUrls[urlField as keyof AuthUrls]);

            // Verify protocol
            if (url.protocol !== 'https:') {
                return {
                    isValid: false,
                    message: `${urlField} must use HTTPS protocol`
                };
            }

            // Verify domain matches organization domain
            const urlDomain = url.hostname;
            if (!urlDomain.endsWith(orgDomain)) {
                return {
                    isValid: false,
                    message: `${urlField} must be under the organization's domain (${orgDomain})`
                };
            }
        } catch {
            return {
                isValid: false,
                message: `Invalid URL format for ${urlField}`
            };
        }
    }

    return {
        isValid: true,
        message: ''
    };
} 