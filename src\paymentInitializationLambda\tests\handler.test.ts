// Mock Stripe instance
const mockStripeInstance = {
  paymentIntents: {
    create: jest.fn().mockResolvedValue({
      id: 'pi_test_123',
      client_secret: 'test_client_secret_123',
      status: 'requires_payment_method'
    })
  }
};

// Mock Stripe constructor
jest.mock('stripe', () => {
  return jest.fn(() => mockStripeInstance);
});

// Add secrets mock
jest.mock('../../shared/secrets', () => ({
  getStripeSecrets: jest.fn().mockResolvedValue({
    secretKey: 'test_stripe_secret_key',
    publishableKey: 'test_stripe_publishable_key',
    webhookSecret: 'test_webhook_secret'
  })
}));

import { APIGatewayProxyEvent } from 'aws-lambda';
import { paymentInitializationLambda } from '../handler';
import { verifyToken } from '../../shared/utils/tokenUtils';
import { 
  createMockEvent, 
  createTestUser,
  expectErrorResponse,
  expectSuccessResponse
} from '../../shared/testUtils/lambdaTestUtils';
import { initializeTestDatabase, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { PaymentStatus } from '../../shared/types/payment';

jest.mock('../../shared/utils/tokenUtils');

describe('Payment Initialization Lambda Handler', () => {
  beforeAll(async () => {
    console.log('Setting up test environment variables...');
    process.env.PAYMENT_HISTORY_TABLE_NAME = 'paymentHistory';
    process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
    process.env.REGION = 'eu-west-1';
    process.env.STRIPE_SECRET_KEY = 'sk_test_51Qvxaz2LMeGvIu8sDTORi0bE4Ybo6GDEWXvHLp2en3X5O0tIKQujv6Rfzxb8B93m6NIMB3vbRu0OGp7RzrGC9WrK00WloK0zrg';
    console.log('Environment variables set:', {
      region: process.env.REGION,
      secretName: process.env.STRIPE_SECRETS_NAME
    });
    await initializeTestDatabase();
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockStripeInstance.paymentIntents.create.mockReset();
  });

  const setupTestUser = async (username: string) => {
    const user = await createTestUser({
      username,
      email: `${username}@example.com`,
      accountType: 'parent',
      parentAccount: 'ROOT',
      accountBalance: 0
    });
    console.log('Created test user:', user);
    await seedTestUser(user);
    
    (verifyToken as jest.Mock).mockReturnValue({
      userID: user.userID,
      email: user.email,
      accountType: 'parent',
      publicKey: user.publicKey
    });
    
    return user;
  };

  const createEventWithAuth = (path: string, body = {}) => {
    const event = createMockEvent(body, path);
    event.headers = {
      ...event.headers,
      Authorization: 'Bearer valid-token'
    };
    return event;
  };

  it.skip('should initialize payment successfully', async () => {
    console.log('Starting payment initialization test...');
    const parentUser = await setupTestUser('parentuser');
    
    const mockPaymentIntent = {
      client_secret: 'test_client_secret_123',
      id: 'pi_test_123',
      status: 'requires_payment_method'
    };
    mockStripeInstance.paymentIntents.create.mockResolvedValue(mockPaymentIntent);
    console.log('Stripe mock setup complete');

    const event = createEventWithAuth('/parent/initialize-payment', { amount: 100 });
    console.log('Test event created:', {
      path: event.path,
      body: event.body
    });
    
    const response = await paymentInitializationLambda(event as APIGatewayProxyEvent);
    console.log('Lambda response received:', {
      statusCode: response.statusCode,
      body: response.body
    });

    expectSuccessResponse(response, 200, {
      clientSecret: mockPaymentIntent.client_secret,
      paymentId: expect.any(String)
    });
  });

  it.skip('should reject requests without authorization header', async () => {
    const event = createMockEvent({}, '/parent/initialize-payment');
    const response = await paymentInitializationLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 401, 'UNAUTHORIZED', 'Missing authorization token');
  });

  it.skip('should reject non-parent accounts', async () => {
    (verifyToken as jest.Mock).mockReturnValue({
      userID: 'child-user-id',
      email: '<EMAIL>',
      accountType: 'child',
      publicKey: 'child-public-key'
    });

    const event = createEventWithAuth('/parent/initialize-payment', { amount: 100 });
    const response = await paymentInitializationLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 401, 'UNAUTHORIZED', 'Only parent accounts can make payments');
  });

  it.skip('should reject invalid amount', async () => {
    const parentUser = await setupTestUser('amountuser');
    const event = createEventWithAuth('/parent/initialize-payment', { amount: -50 });
    const response = await paymentInitializationLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_AMOUNT', 'Amount must be greater than 0');
  });
}); 