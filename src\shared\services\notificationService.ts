import axios from 'axios';
import { Environment } from './environment';
import { APIGatewayProxyEvent } from 'aws-lambda';

export class NotificationService {
  /**
   * Sends a notification to Telegram about a new signup with retry logic
   */
  public static async sendSignupNotification(
    username: string,
    email: string,
    event?: APIGatewayProxyEvent
  ): Promise<void> {
    const maxRetries = 2;
    let attempt = 0;

    while (attempt <= maxRetries) {
      try {
        await this.sendTelegramMessage(username, email, attempt + 1);
        return; // Success, exit
      } catch (error) {
        attempt++;
        if (attempt > maxRetries) {
          console.error(`[TELEGRAM] Failed after ${maxRetries + 1} attempts:`, error);
          return; // Give up after max retries
        }
        console.warn(`[TELEGRAM] Attempt ${attempt} failed, retrying...`);
        await this.delay(1000 * attempt); // Exponential backoff
      }
    }
  }

  /**
   * Helper method to send the actual Telegram message
   */
  private static async sendTelegramMessage(
    username: string,
    email: string,
    attempt: number
  ): Promise<void> {
    try {
      // Get Telegram credentials directly from environment variables
      const botToken = process.env.TELEGRAM_BOT_TOKEN;
      const chatId = process.env.TELEGRAM_CHAT_ID;

      console.log(`[TELEGRAM] Notification attempt ${attempt}:`, {
        hasBotToken: !!botToken,
        botTokenLength: botToken?.length || 0,
        hasChatId: !!chatId,
        chatId: chatId,
        username: username,
        email: email
      });

      if (!botToken || !chatId) {
        console.log('[TELEGRAM] Notification skipped: Missing bot token or chat ID', {
          botToken: botToken ? 'present' : 'missing',
          chatId: chatId ? 'present' : 'missing'
        });
        return;
      }

      const message = `🎉 New signup!\nUsername: ${username}\nEmail: ${email}`;
      const url = `https://api.telegram.org/bot${botToken}/sendMessage`;

      console.log('[TELEGRAM] Sending message:', {
        url: url.replace(botToken, 'BOT_TOKEN_HIDDEN'),
        messageLength: message.length
      });

      // Add timeout to prevent hanging
      const response = await axios.post(url, {
        chat_id: chatId,
        text: message,
        parse_mode: 'HTML'
      }, {
        timeout: 5000, // 5 second timeout (reduced for faster retries)
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('[TELEGRAM] Notification sent successfully:', {
        status: response.status,
        statusText: response.statusText,
        messageId: response.data?.result?.message_id,
        telegramResponse: response.data
      });
    } catch (error: any) {
      console.error('[TELEGRAM] Failed to send notification:', {
        error: error.message,
        code: error.code,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        timeout: error.code === 'ECONNABORTED',
        username: username,
        email: email,
        stack: error.stack?.split('\n')[0] // First line of stack trace
      });

      // Log specific error types
      if (error.code === 'ECONNABORTED') {
        console.error('[TELEGRAM] Request timed out after 10 seconds');
      } else if (error.response?.status === 400) {
        console.error('[TELEGRAM] Bad request - check bot token and chat ID');
      } else if (error.response?.status === 401) {
        console.error('[TELEGRAM] Unauthorized - invalid bot token');
      } else if (error.response?.status === 403) {
        console.error('[TELEGRAM] Forbidden - bot not added to chat or no permissions');
      }

      // Non-blocking, so we just log the error
      throw error; // Re-throw for retry logic
    }
  }

  /**
   * Helper method to add delay between retries
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
