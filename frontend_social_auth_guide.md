# Frontend Social Authentication Guide

## GitHub OAuth URL Limitation Solution

### **Problem**
GitHub OAuth apps only allow:
- 1 Authorization callback URL
- 1 Homepage URL

But you want social signin on both signup and signin pages.

### **Solution: Single Callback with State Parameter**

## **Step 1: Configure GitHub OAuth App**

Set your GitHub OAuth app with:
- **Authorization callback URL**: `https://yourdomain.com/auth/callback`
- **Homepage URL**: `https://yourdomain.com`

## **Step 2: Frontend Implementation**

### **A. Signup Page (`/signup`)**

```javascript
// signup.js
function initiateGitHubSignup() {
    const clientId = 'Ov23liXWQrmLoHsB4CSj';
    const redirectUri = 'https://yourdomain.com/auth/callback';
    const state = btoa(JSON.stringify({
        action: 'signup',
        returnTo: '/dashboard', // Where to go after successful signup
        timestamp: Date.now()
    }));
    
    const githubAuthUrl = `https://github.com/login/oauth/authorize?` +
        `client_id=${clientId}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `scope=user:email&` +
        `state=${encodeURIComponent(state)}`;
    
    window.location.href = githubAuthUrl;
}

// Add to your signup button
document.getElementById('github-signup-btn').addEventListener('click', initiateGitHubSignup);
```

### **B. Signin Page (`/signin`)**

```javascript
// signin.js
function initiateGitHubSignin() {
    const clientId = 'Ov23liXWQrmLoHsB4CSj';
    const redirectUri = 'https://yourdomain.com/auth/callback';
    const state = btoa(JSON.stringify({
        action: 'signin',
        returnTo: '/dashboard', // Where to go after successful signin
        timestamp: Date.now()
    }));
    
    const githubAuthUrl = `https://github.com/login/oauth/authorize?` +
        `client_id=${clientId}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `scope=user:email&` +
        `state=${encodeURIComponent(state)}`;
    
    window.location.href = githubAuthUrl;
}

// Add to your signin button
document.getElementById('github-signin-btn').addEventListener('click', initiateGitHubSignin);
```

### **C. Callback Handler (`/auth/callback`)**

```javascript
// callback.js
async function handleAuthCallback() {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    const error = urlParams.get('error');
    
    // Handle OAuth errors
    if (error) {
        console.error('OAuth error:', error);
        showError('Authentication failed. Please try again.');
        return;
    }
    
    if (!code || !state) {
        showError('Invalid authentication response.');
        return;
    }
    
    try {
        // Decode state to understand the original action
        const stateData = JSON.parse(atob(decodeURIComponent(state)));
        console.log('Auth action:', stateData.action);
        
        // Call your backend with the code
        const response = await fetch('https://staging.api.authiqa.com/auth/github', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                code: code,
                parentPublicKey: getParentPublicKey() // Your existing function
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Store tokens
            localStorage.setItem('accessToken', result.token);
            if (result.user) {
                sessionStorage.setItem('jwtSecret', result.user.jwtSecret || '');
            }
            
            // Redirect based on original action
            const returnTo = stateData.returnTo || '/dashboard';
            
            if (stateData.action === 'signup') {
                // Handle post-signup logic
                showSuccess('Account created successfully!');
                window.location.href = returnTo;
            } else if (stateData.action === 'signin') {
                // Handle post-signin logic
                showSuccess('Signed in successfully!');
                window.location.href = returnTo;
            }
        } else {
            showError(result.message || 'Authentication failed.');
        }
        
    } catch (error) {
        console.error('Callback handling error:', error);
        showError('Authentication processing failed.');
    }
}

// Helper functions
function getParentPublicKey() {
    // Your existing logic to get the parent public key
    return document.querySelector('[data-public-key]')?.getAttribute('data-public-key');
}

function showSuccess(message) {
    // Your success notification logic
    console.log('Success:', message);
}

function showError(message) {
    // Your error notification logic
    console.error('Error:', message);
}

// Run callback handler when page loads
if (window.location.pathname === '/auth/callback') {
    handleAuthCallback();
}
```

## **Step 3: Google OAuth Implementation**

Google OAuth is more flexible and allows multiple redirect URIs, but for consistency, use the same pattern:

### **Google Signup/Signin**

```javascript
// For Google, you can use the same state parameter approach
function initiateGoogleAuth(action) {
    const state = btoa(JSON.stringify({
        action: action, // 'signup' or 'signin'
        returnTo: '/dashboard',
        timestamp: Date.now()
    }));
    
    // Initialize Google OAuth with state
    google.accounts.oauth2.initTokenClient({
        client_id: 'your-google-client-id',
        scope: 'email profile',
        state: state,
        callback: handleGoogleCallback
    }).requestAccessToken();
}
```

## **Step 4: Security Considerations**

### **State Parameter Validation**

```javascript
function validateState(stateData) {
    // Check timestamp (prevent replay attacks)
    const maxAge = 10 * 60 * 1000; // 10 minutes
    if (Date.now() - stateData.timestamp > maxAge) {
        throw new Error('Authentication session expired');
    }
    
    // Validate action
    if (!['signup', 'signin'].includes(stateData.action)) {
        throw new Error('Invalid authentication action');
    }
    
    return true;
}
```

### **Error Handling**

```javascript
function handleAuthError(error, action) {
    const errorMessages = {
        'access_denied': 'You cancelled the authentication process.',
        'invalid_request': 'Authentication request was invalid.',
        'server_error': 'Authentication server error. Please try again.'
    };
    
    const message = errorMessages[error] || 'Authentication failed. Please try again.';
    showError(message);
    
    // Redirect back to appropriate page
    const redirectPage = action === 'signup' ? '/signup' : '/signin';
    setTimeout(() => {
        window.location.href = redirectPage;
    }, 3000);
}
```

## **Step 5: Testing Checklist**

- [ ] GitHub signup flow works
- [ ] GitHub signin flow works  
- [ ] Google signup flow works
- [ ] Google signin flow works
- [ ] State parameter validation works
- [ ] Error handling works
- [ ] Token storage works
- [ ] Redirect after auth works
- [ ] Expired state handling works

## **Benefits of This Approach**

1. **Single OAuth app** - No need for multiple GitHub apps
2. **Flexible routing** - Handle different user flows
3. **Security** - State parameter prevents CSRF attacks
4. **Consistency** - Same pattern for both Google and GitHub
5. **Maintainable** - Centralized callback handling
