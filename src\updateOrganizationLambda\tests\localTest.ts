import { updateOrganizationLambda } from '../handler';
import { APIGatewayProxyEvent } from 'aws-lambda';
import { createMockEvent, createTestUser } from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { getTestUser } from '../../shared/testUtils/databaseTestUtils';
import { generateToken } from '../../shared/utils/tokenUtils';

async function runLocalTest() {
  process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
  process.env.JWT_SECRET = 'test-jwt-secret';
  console.log('Running local test for Update Organization Lambda');

  const testUser = await createTestUser({
    emailVerified: true,
    organizationUpdateCount: 0
  });
  
  await seedTestUser(testUser);
  
  const event = createMockEvent({
    organizationName: 'Local Test Organization',
    organizationUrl: 'https://localtest.com',
    authUrls: {
      signupUrl: 'https://auth.localtest.com/signup',
      signinUrl: 'https://auth.localtest.com/signin',
      emailVerificationUrl: 'https://auth.localtest.com/verify',
      resetPasswordUrl: 'https://auth.localtest.com/reset',
      updatePasswordUrl: 'https://auth.localtest.com/update',
      resendVerificationUrl: 'https://auth.localtest.com/resend',
      successfulUrl: 'https://app.localtest.com/dashboard'
    }
  }, '/auth/update-organization');
  if (!event.headers) {
    event.headers = {}; // Initialize headers if undefined
  }
  const token = generateToken({
    userID: testUser.userID,
    email: testUser.email,
    accountType: testUser.accountType
  });
  event.headers['Authorization'] = `Bearer ${token}`;

  const result = await updateOrganizationLambda(event as APIGatewayProxyEvent);
  console.log('Result:', result);

  if (result.statusCode === 200) {
    const updatedUser = await getTestUser(testUser.userID);
    console.log('Updated user:', updatedUser);
  }

  await clearTestData();
}

runLocalTest().catch(console.error); 