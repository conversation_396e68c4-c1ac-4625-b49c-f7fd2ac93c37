import { createTestUser } from '../testUtils/lambdaTestUtils';
import { getTestUser, updateTestUser } from '../testUtils/databaseTestUtils';
import { 
  incrementEmailConfirmationCount,
  incrementResendEmailCount,
  incrementResetPasswordRequestCount,
  incrementPasswordUpdateCount,
  incrementSignInCount,
  incrementOrganizationDetailsRetrievalCount,
  incrementChildAccountsListRetrievalCount
} from './userOperations';
import { seedTestUser, initializeTestDatabase } from '../testUtils/databaseLifecycle';
import { dynamoDB, TABLE_NAME } from '../database';

describe('User Operation Counters', () => {
  beforeAll(async () => {
    process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
    await initializeTestDatabase();
  });

  afterAll(async () => {
    delete process.env.USER_DETAILS_TABLE_NAME;
  });

  it.skip('should initialize counters to 0 for new user', async () => {
    const testUser = await createTestUser({});
    await seedTestUser(testUser);
    
    const user = await getTestUser(testUser.userID);
    expect(user).toBeDefined();
    if (!user) throw new Error('User not found');
    
    expect(user.emailConfirmationCount).toBe(0);
    expect(user.resendEmailCount).toBe(0);
    expect(user.resetPasswordRequestCount).toBe(0);
    expect(user.passwordUpdateCount).toBe(0);
  });

  it.skip('should increment email confirmation count', async () => {
    const testUser = await createTestUser({});
    await seedTestUser(testUser);
    
    await incrementEmailConfirmationCount(testUser.userID);
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.emailConfirmationCount).toBe(1);
  });

  it.skip('should increment resend email count', async () => {
    const testUser = await createTestUser({});
    await seedTestUser(testUser);
    
    await incrementResendEmailCount(testUser.userID);
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.resendEmailCount).toBe(1);
  });

  it.skip('should increment reset password request count', async () => {
    const testUser = await createTestUser({});
    await seedTestUser(testUser);
    
    await incrementResetPasswordRequestCount(testUser.userID);
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.resetPasswordRequestCount).toBe(1);
  });

  it.skip('should increment password update count', async () => {
    const testUser = await createTestUser({});
    await seedTestUser(testUser);
    
    await incrementPasswordUpdateCount(testUser.userID);
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.passwordUpdateCount).toBe(1);
  });

  // Add test for concurrent updates
  it.skip('should handle concurrent updates correctly', async () => {
    const testUser = await createTestUser({});
    await seedTestUser(testUser);
    
    // Simulate multiple concurrent increments
    await Promise.all([
      incrementEmailConfirmationCount(testUser.userID),
      incrementEmailConfirmationCount(testUser.userID),
      incrementEmailConfirmationCount(testUser.userID)
    ]);

    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.emailConfirmationCount).toBe(3);
  });

  it.skip('should increment sign in count', async () => {
    const testUser = await createTestUser({});
    await seedTestUser(testUser);
    
    await incrementSignInCount(testUser.userID);
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.signInCount).toBe(1);
  });

  it.skip('should increment organization details retrieval count', async () => {
    const testUser = await createTestUser({});
    await seedTestUser(testUser);
    
    await incrementOrganizationDetailsRetrievalCount(testUser.userID);
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.organizationDetailsRetrievalCount).toBe(1);
  });

  it.skip('should increment child accounts list retrieval count', async () => {
    const testUser = await createTestUser({});
    await seedTestUser(testUser);
    
    await incrementChildAccountsListRetrievalCount(testUser.userID);
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.childAccountsListRetrievalCount).toBe(1);
  });

  it.skip('should reset counts at the start of new month', async () => {
    const testUser = await createTestUser({});
    await seedTestUser(testUser);
    
    // Set some counts
    await incrementEmailConfirmationCount(testUser.userID);
    await incrementSignInCount(testUser.userID);
    
    // Simulate last reset being in previous month
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);
    
    await updateTestUser(testUser.userID, {
      lastResetDate: lastMonth.getTime()
    });
    
    // Trigger a new operation which should cause reset
    await incrementEmailConfirmationCount(testUser.userID);
    
    // Verify counts were reset
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.emailConfirmationCount).toBe(1); // Only the new increment
    expect(updatedUser?.signInCount).toBe(0); // Reset to zero
  });

  it.skip('should reset all operation counts at the start of new month', async () => {
    const testUser = await createTestUser({
      emailConfirmationCount: 5,
      resendEmailCount: 3,
      resetPasswordRequestCount: 2,
      passwordUpdateCount: 1,
      signInCount: 10,
      organizationDetailsRetrievalCount: 4,
      childAccountsListRetrievalCount: 6
    });
    await seedTestUser(testUser);
    
    // Simulate last reset being in previous month
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);
    
    await updateTestUser(testUser.userID, {
      lastResetDate: lastMonth.getTime()
    });
    
    // Trigger reset by performing any operation
    await incrementEmailConfirmationCount(testUser.userID);
    
    // Verify all counts were reset
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.emailConfirmationCount).toBe(1); // Only the new increment
    expect(updatedUser?.resendEmailCount).toBe(0);
    expect(updatedUser?.resetPasswordRequestCount).toBe(0);
    expect(updatedUser?.passwordUpdateCount).toBe(0);
    expect(updatedUser?.signInCount).toBe(0);
    expect(updatedUser?.organizationDetailsRetrievalCount).toBe(0);
    expect(updatedUser?.childAccountsListRetrievalCount).toBe(0);
  });
}); 