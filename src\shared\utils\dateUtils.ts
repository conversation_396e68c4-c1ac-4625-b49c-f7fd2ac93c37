export const parseDateInput = (dateStr: string): number => {
    // If it's already a timestamp (number string)
    if (/^\d+$/.test(dateStr)) {
        return parseInt(dateStr);
    }

    // Try parsing ISO 8601 or YYYY-MM-DD format
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
        throw new Error('Invalid date format');
    }

    return date.getTime();
};
export const validateDateFormat = (date: string): boolean => {
  // Validate YYYY-MM format
  const dateRegex = /^\d{4}-(?:0[1-9]|1[0-2])$/;
  return dateRegex.test(date);
};
