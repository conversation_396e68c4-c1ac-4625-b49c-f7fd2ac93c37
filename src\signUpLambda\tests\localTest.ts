import { signUpLambda } from '../handler';
import { APIGatewayProxyEvent } from 'aws-lambda';
import { createMockEvent } from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData } from '../../shared/testUtils/databaseLifecycle';

async function runLocalTest() {
  console.log('Running local test for Sign Up Lambda');
  
  try {
    await clearTestData();
    
    const event = createMockEvent({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'StrongP@ssw0rd123'
    }, '/signup');
    
    const result = await signUpLambda(event as APIGatewayProxyEvent);
    console.log('Result:', result);

    await clearTestData();
  } catch (error) {
    console.error('Error:', error);
  }
}

runLocalTest().catch(console.error);
