import { 
    Scan<PERSON>ommand, 
    DeleteCommand, 
    PutCommand,
    GetCommand
} from "@aws-sdk/lib-dynamodb";
import { CreateTableCommand, DeleteTableCommand, DescribeTableCommand } from "@aws-sdk/client-dynamodb";
import { dynamoDB, dynamoDBClient } from '../database';

const BILLING_TABLE_NAME = 'authiqaBilling';

interface BillingRecord {
    publicKey: string;
    monthYear: string;
    totalAccounts: number;
    costAssociatedWithAccounts: number;
    totalIOInteractions: number;
    costAssociatedWithIO: number;
    totalFinalCost: number;
    timestamp: number;
}

export async function clearBillingTestData(): Promise<void> {
    const result = await dynamoDBClient.send(new ScanCommand({
        TableName: BILLING_TABLE_NAME
    }));

    if (result.Items) {
        for (const item of result.Items) {
            await dynamoDBClient.send(new DeleteCommand({
                TableName: BILLING_TABLE_NAME,
                Key: { 
                    publicKey: item.publicKey,
                    monthYear: item.monthYear
                }
            }));
        }
    }
}

export async function seedBillingRecords(records: BillingRecord[]): Promise<void> {
    try {
        for (const record of records) {
            await dynamoDBClient.send(new PutCommand({
                TableName: BILLING_TABLE_NAME,
                Item: record
            }));
        }
    } catch (error) {
        console.error('Error seeding billing records:', error);
        throw error;
    }
}

export const getBillingRecord = async (publicKey: string, monthYear: string) => {
    const result = await dynamoDBClient.send(new GetCommand({
        TableName: BILLING_TABLE_NAME,
        Key: { 
            publicKey,
            monthYear
        }
    }));
    return result.Item as BillingRecord | undefined;
};