// Email verification constants
export const VERIFICATION_TOKEN_EXPIRY = 15 * 60 * 1000; // 15 minutes
export const VERIFICATION_RESEND_COOLDOWN = 60 * 1000; // 1 minute

// Password reset constants (keep OTP terminology)
export const OTP_EXPIRY_TIME = 15 * 60 * 1000; // 15 minutes
export const OTP_RESEND_COOLDOWN = 60 * 1000; // 1 minute

// Password expiry constants
export const PASSWORD_EXPIRY_DAYS = 90; // 90 days
export const PASSWORD_EXPIRY_MS = PASSWORD_EXPIRY_DAYS * 24 * 60 * 60 * 1000; // 90 days in milliseconds

export const OPERATION_COSTS = {
  emailConfirmation: { reads: 2, writes: 1 },
  resendConfirmation: { reads: 1, writes: 1 },
  resetPassword: { reads: 1, writes: 1 },
  updatePassword: { reads: 1, writes: 1 },
  childAccounts: { reads: 2, writes: 1 },
  signIn: { reads: 2, writes: 1 },
  organizationUpdate: { reads: 1, writes: 1 },
  organizationDetailsRetrieval: { reads: 1, writes: 1 },
  childAccountsListRetrieval: { reads: 2, writes: 1 }
};

export const COST_CALCULATOR_OPERATIONS = {
    reads: 2,  // getParentAccountOperations + getChildAccountsByParent
    writes: 0,
    expectedCalls: 10  // Assuming it's called 10 times
};

export const AUTHIQA_MARGIN = 0.40; // 40% margin

// DynamoDB prices per million operations
export const PRICES = {
  read: 0.1415,  // per million reads
  write: 0.705   // per million writes
};

// Storage costs
export const STORAGE_COST_PER_GB = 0.283; // $0.283 per GB
export const STORAGE_COST_PER_KB = STORAGE_COST_PER_GB / 1048576; // Cost per KB
