  import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
  import <PERSON><PERSON> from 'stripe';
  import { v4 as uuidv4 } from 'uuid';
  import { finalizePayment, handleFailedPayment } from '../shared/services/balanceService';
  import { getStripeSecrets } from '../shared/secrets';
  import { WebhookSignatureError, PaymentProcessingError, PaymentMetadataError } from '../shared/types/errors';
  import { getPaymentById } from '../shared/database/paymentOperations';
  import { PaymentStatus } from '../shared/types/payment';
  import { getConfig } from '../shared/services/configService';

  export async function stripeWebhookLambda(
    event: APIGatewayProxyEvent
  ): Promise<APIGatewayProxyResult> {
    const transactionId = uuidv4();
    
    // Load configuration from the service
    const config = await getConfig(event);
    
    console.log(`[${transactionId}] Starting webhook processing`, {
      environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
      paymentTable: config.PAYMENT_HISTORY_TABLE_NAME,
      userTable: config.USER_DETAILS_TABLE_NAME
    });

    try {
      // Get secrets from AWS Secrets Manager with environment awareness
      const secrets = await getStripeSecrets(event);
      // Log masked versions of the secrets for debugging
      console.log(`[${transactionId}] Stripe secrets retrieved:`, {
        secretKeyPrefix: secrets.secretKey.substring(0, 7) + '...',
        secretKeyLength: secrets.secretKey.length,
        publishableKeyPrefix: secrets.publishableKey.substring(0, 7) + '...',
        publishableKeyLength: secrets.publishableKey.length,
        webhookSecretPrefix: secrets.webhookSecret.substring(0, 7) + '...',
        webhookSecretLength: secrets.webhookSecret.length,
        environment: event.headers.host?.includes('staging') ? 'staging' : 'live'
      });

      const stripe = new Stripe(secrets.secretKey, {
        apiVersion: '2025-02-24.acacia' as Stripe.LatestApiVersion
      });
      
      
      // Use config.NODE_ENV instead of hardcoded check
      const isLocalDev = config.NODE_ENV === 'development' || config.NODE_ENV === 'local';

      let stripeEvent;

      if (isLocalDev) {
        // Skip signature verification in local development
        stripeEvent = JSON.parse(event.body || '{}');
        console.log(`[${transactionId}] Local development: Skipping signature verification`);
      } else {
        // Verify signature in production
        const sig = event.headers['stripe-signature'];
        if (!sig || !event.body) {
          throw new WebhookSignatureError('Missing signature or payload');
        }

        try {
          stripeEvent = stripe.webhooks.constructEvent(
            event.body,
            sig,
            secrets.webhookSecret
          );
        } catch (error) {
          throw new WebhookSignatureError(`Invalid signature: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      
      console.log(`[${transactionId}] Processing webhook event: ${stripeEvent.type}`);

      switch (stripeEvent.type) {
        case 'payment_intent.succeeded':
          const paymentIntent = stripeEvent.data.object as Stripe.PaymentIntent;
          const paymentId = paymentIntent.metadata.paymentId;
          
          if (!paymentId) {
            throw new PaymentMetadataError('Payment ID not found in metadata');
          }

          // Get payment and check its status
          const existingPayment = await getPaymentById(paymentId, config.PAYMENT_HISTORY_TABLE_NAME);
          if (!existingPayment) {
            console.error(`[${transactionId}] Webhook: Payment record not found for ID: ${paymentId}. This should not happen.`);
            return {
              statusCode: 404,
              body: JSON.stringify({ 
                error: 'Payment record not found',
                transactionId 
              })
            };
          }

          
          if (existingPayment.status === PaymentStatus.COMPLETED) {
            console.log(`[${transactionId}] Webhook: Payment ${paymentId} is already completed. Skipping finalizePayment.`);
            return {
              statusCode: 200,
              body: JSON.stringify({ 
                received: true, 
                duplicate: true,
                transactionId 
              })
            };
          }

          console.log(`[${transactionId}] Processing successful payment: ${paymentId}`);
          await finalizePayment(
            paymentId, 
            paymentIntent.id, 
            config.PAYMENT_HISTORY_TABLE_NAME,
            config.USER_DETAILS_TABLE_NAME
          );
          console.log(`[${transactionId}] Payment finalized successfully: ${paymentId}`);
          break;

          
        case 'payment_intent.failed':
          const failedPayment = stripeEvent.data.object as Stripe.PaymentIntent;
          const failedPaymentId = failedPayment.metadata.paymentId;
          
          if (!failedPaymentId) {
            throw new PaymentMetadataError('Payment ID not found in metadata');
          }

          console.log(`[${transactionId}] Processing failed payment: ${failedPaymentId}`);
          console.log(`[${transactionId}] Failure reason: ${failedPayment.last_payment_error?.message}`);
          await handleFailedPayment(
            failedPaymentId, 
            failedPayment.id,
            config.PAYMENT_HISTORY_TABLE_NAME,
            config.USER_DETAILS_TABLE_NAME  // Add user table name
          );
          console.log(`[${transactionId}] Failed payment handled: ${failedPaymentId}`);
          break;

        default:
          console.log(`[${transactionId}] Unhandled event type: ${stripeEvent.type}`);
      }

      return {
        statusCode: 200,
        body: JSON.stringify({ 
          received: true,
          transactionId
        })
      };
    } catch (err: unknown) {
      const error = err as Error;
      console.error(`[${transactionId}] Error processing webhook:`, {
        errorType: error.constructor.name,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        errorStack: error instanceof Error ? error.stack : undefined
      });

      // Return appropriate status codes based on error type
      if (error instanceof WebhookSignatureError) {
        return {
          statusCode: 401,
          body: JSON.stringify({
            error: 'Webhook signature verification failed',
            transactionId
          })
        };
      }

      if (error instanceof PaymentMetadataError) {
        return {
          statusCode: 422,
          body: JSON.stringify({
            error: 'Invalid payment metadata',
            transactionId
          })
        };
      }

      if (error instanceof PaymentProcessingError) {
        return {
          statusCode: 500,
          body: JSON.stringify({
            error: 'Payment processing failed',
            transactionId
          })
        };
      }

      return {
        statusCode: 400,
        body: JSON.stringify({
          error: `Webhook Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          transactionId
        })
      };
    }
  } 
