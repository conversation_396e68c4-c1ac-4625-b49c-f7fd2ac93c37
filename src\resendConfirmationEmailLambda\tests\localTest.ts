import { resendConfirmationEmailLambda } from '../handler';
import { APIGatewayProxyEvent, Context, Callback, APIGatewayProxyResult } from 'aws-lambda';
import { createMockEvent, createMockContext, createTestUser } from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, seedTestUser } from '../../shared/testUtils/databaseLifecycle';

async function runLocalTest() {
  console.log('Running local test for Resend Confirmation Email Lambda');
  
  try {
    await clearTestData();
    
    const testUser = await createTestUser({
      emailVerified: false,
      lastVerificationTokenSentAt: Date.now() - 6 * 60 * 1000
    });
    
    await seedTestUser(testUser);
    
    const event = createMockEvent({
      email: testUser.email
    }, '/resend-confirmation-email');
    
    const context = createMockContext() as Context;
    const callback: Callback<APIGatewayProxyResult> = (error, result) => {};
    
    const result = await resendConfirmationEmailLambda(
      event as APIGatewayProxyEvent, 
      
    );
    console.log('Result:', result);
    
    await clearTestData();
  } catch (error) {
    console.error('Error:', error);
  }
}

runLocalTest().catch(console.error);
