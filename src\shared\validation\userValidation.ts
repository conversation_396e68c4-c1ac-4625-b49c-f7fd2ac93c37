export interface ValidationResult {
  isValid: boolean;
  message: string;
}

interface UserValidationResult {
  isValid: boolean;
  error?: {
    code: string;
    message: string;
  };
}

export interface SignUpValidationInput {
  username?: string;
  email: string;
  password: string;
  parentPublicKey?: string;
}

export const validateEmail = (email: string): UserValidationResult => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const isValid = emailRegex.test(email);
  
  return isValid ? { isValid: true } : {
    isValid: false,
    error: {
      code: 'INVALID_EMAIL_FORMAT',
      message: 'Email address is not in a valid format'
    }
  };
};

export const validatePassword = (password: string): UserValidationResult => {
  const specialChars = '!@#$%^&*()_+-=[]{};\'",./<>?\\|';
  const escapedSpecialChars = specialChars.split('').map(char => '\\' + char).join('');
  
  const passwordRegex = new RegExp(
    `^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[${escapedSpecialChars}])[A-Za-z\\d${escapedSpecialChars}]{8,}$`
  );
  const isValid = passwordRegex.test(password);
  
  return isValid ? { isValid: true } : {
    isValid: false,
    error: {
      code: 'INVALID_PASSWORD_FORMAT',
      message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    }
  };
};

export const validateUsername = (username: string): UserValidationResult => {
  if (username.length > 30) {
    return {
      isValid: false,
      error: {
        code: 'INVALID_USERNAME_FORMAT',
        message: 'Username must not exceed 30 characters'
      }
    };
  }

  const usernameRegex = /^[a-zA-Z0-9_]+$/;
  if (!usernameRegex.test(username)) {
    return {
      isValid: false,
      error: {
        code: 'INVALID_USERNAME_FORMAT',
        message: 'Username can only contain letters, numbers, and underscores'
      }
    };
  }

  return {
    isValid: true
  };
};

export async function validateSignUpInput(input: SignUpValidationInput): Promise<UserValidationResult> {
  // Email validation
  const emailResult = validateEmail(input.email);
  if (!emailResult.isValid) {
    return emailResult;
  }

  // Username validation
  if (input.username) {
    const usernameResult = validateUsername(input.username);
    if (!usernameResult.isValid) {
      return usernameResult;
    }
  }

  // Password validation 
  const passwordResult = validatePassword(input.password);
  if (!passwordResult.isValid) {
    return passwordResult;
  }

  // ParentpublicKey format validation if provided
  if (input.parentPublicKey && !input.parentPublicKey.match(/^APK_[a-f0-9]{32}_\d+$/)) {
    return {
      isValid: false,
      error: {
        code: 'INVALID_PARENT_PUBLIC_KEY_FORMAT',
        message: 'Invalid Public key format'
      }
    };
  }

  return { isValid: true };
}
