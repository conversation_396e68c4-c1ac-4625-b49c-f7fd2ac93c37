import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ErrorTypes } from '../shared/errorTypes';
import { getUserByPublicKey, getUserByEmail, getUserByEmailAndParent, incrementSignInCount } from '../shared/database/userOperations';
import { verifyPassword } from '../shared/utils/passwordUtils';
import { generateToken } from '../shared/utils/tokenUtils';
import { manageLoginAttempts } from '../shared/loginAttempts';
import { ErrorResponse, SuccessResponse } from '../shared/responseUtils';
import { getConfig } from '../shared/services/configService';

interface SignInRequest {
  email: string;
  password: string;
  parentPublicKey?: string;
}

/**
 * Validates the request body and extracts the required fields
 */
function validateRequestBody(body: string | null): SignInRequest {
  if (!body) {
    throw ErrorResponse(400, 'MISSING_REQUEST_BODY', 'Request body is required');
  }

  let requestBody;
  try {
    requestBody = JSON.parse(body);
  } catch {
    throw ErrorResponse(400, 'INVALID_REQUEST_BODY', 'Invalid JSON in request body');
  }

  const { email, password, parentPublicKey } = requestBody;
  if (!email || !password) {
    throw ErrorResponse(400, 'MISSING_REQUIRED_FIELDS', 'Email and password are required');
  }

  return { email, password, parentPublicKey };
}

/**
 * Validates account type specific requirements
 */
async function validateAccountRequirements(user: any, parentPublicKey?: string, tableName?: string): Promise<void> {
  // For child accounts, parent public key is mandatory
  if (user.accountType === 'child') {
    console.log(`[SIGNIN] Validating child account ${user.userID} with parentPublicKey: ${parentPublicKey}`);

    if (!parentPublicKey) {
      throw ErrorResponse(400, 'MISSING_PARENT_PUBLIC_KEY', 'Parent public key is required for child accounts');
    }

    // Verify the parent public key matches
    if (user.parentAccount !== parentPublicKey) {
      console.log(`[SIGNIN] Parent key mismatch for child ${user.userID}: expected ${user.parentAccount}, got ${parentPublicKey}`);
      throw ErrorResponse(401, 'INVALID_PARENT_PUBLIC_KEY', 'Invalid parent public key');
    }

    // Check parent account status
    const parentUser = await getUserByPublicKey(user.parentAccount, tableName);
    if (!parentUser || parentUser.availableBalance === undefined || parentUser.availableBalance <= 0) {
      console.log(`[SIGNIN] Parent account ${user.parentAccount} inactive or insufficient balance`);
      throw ErrorResponse(403, 'PARENT_ACCOUNT_INACTIVE', 'Parent account has insufficient balance for child operations');
    }

    
    console.log(`[SIGNIN] Child account ${user.userID} validation successful`);
  }
  else if (user.accountType === 'parent') {
    if (parentPublicKey) {
      // NEW LOGIC: Organization ID approach for promoted accounts
      console.log(`[SIGNIN] Parent account ${user.userID} attempting widget authentication with parentPublicKey: ${parentPublicKey}`);

      // Step 1: Check if this is a promoted account
      if (user.isPromotedAccount !== true) {
        console.log(`[SIGNIN] Parent account ${user.userID} not promoted - using standard parent account error message`);
        throw ErrorResponse(400, 'INVALID_REQUEST', 'Parent public key should not be provided for parent accounts');
      }

      console.log(`[SIGNIN] Account ${user.userID} is promoted, checking organization validation`);

      // Step 2: Get organization of the provided parent key
      const parentUser = await getUserByPublicKey(parentPublicKey, tableName);
      if (!parentUser) {
        console.log(`[SIGNIN] Parent account ${parentPublicKey} not found`);
        throw ErrorResponse(404, 'PARENT_NOT_FOUND', 'Parent account not found');
      }

      const keyOwnerOrganization = parentUser.organizationName;
      if (!keyOwnerOrganization) {
        console.log(`[SIGNIN] Parent account ${parentPublicKey} has no organization name`);
        throw ErrorResponse(400, 'PARENT_NO_ORGANIZATION', 'Parent account has no organization');
      }

      console.log(`[SIGNIN] Parent key ${parentPublicKey} belongs to organization: ${keyOwnerOrganization}`);

      // Step 3: Check if user's organization matches key owner's organization
      if (user.organizationId !== keyOwnerOrganization) {
        console.log(`[SIGNIN] Organization mismatch for user ${user.userID}: user org "${user.organizationId}" vs key owner org "${keyOwnerOrganization}"`);
        throw ErrorResponse(401, 'ORGANIZATION_MISMATCH', 'Account organization does not match key owner');
      }

      console.log(`[SIGNIN] Organization match confirmed: ${user.organizationId}`);

      // Step 4: Check if this organization is specially authorized
      const { isSpecialOrganization } = await import('../shared/config/organizationConfig');
      if (!isSpecialOrganization(keyOwnerOrganization)) {
        console.log(`[SIGNIN] Organization "${keyOwnerOrganization}" not authorized for widget authentication`);
        throw ErrorResponse(403, 'ORGANIZATION_NOT_AUTHORIZED', 'Organization not authorized for widget authentication');
      }

      console.log(`[SIGNIN] Organization "${keyOwnerOrganization}" is authorized for widget authentication`);

      // Step 5: Verify parent is still active
      if (parentUser.availableBalance === undefined || parentUser.availableBalance <= 0) {
        console.log(`[SIGNIN] Parent account ${parentPublicKey} inactive or insufficient balance`);
        throw ErrorResponse(403, 'PARENT_INACTIVE', 'Parent account is not active');
      }

      console.log(`[SIGNIN] Widget authentication successful for promoted account ${user.userID}`);
      // All checks passed - allow signin

    } else {
      // Standard parent account signin (no parentPublicKey provided)
      console.log(`[SIGNIN] Standard parent account signin for ${user.userID}`);

      // Verify it's a root parent account
      if (user.parentAccount !== 'ROOT') {
        console.log(`[SIGNIN] Parent account ${user.userID} has invalid parentAccount: ${user.parentAccount}`);
        throw ErrorResponse(401, 'INVALID_CREDENTIALS', 'Invalid email or password');
      }

      console.log(`[SIGNIN] Standard parent account ${user.userID} validation successful`);
    }
  }

  // Check if email verification is required for this organization
  let emailVerificationRequired = false;  // Default to false
  if (user.accountType === 'child') {
    // For child accounts, check parent's email verification setting
    const parentUser = await getUserByPublicKey(user.parentAccount, tableName);
    emailVerificationRequired = parentUser?.emailVerificationRequired === true;  // Only require if explicitly true
  } else {
    // For parent accounts, check their own setting
    emailVerificationRequired = user.emailVerificationRequired === true;  // Only require if explicitly true
  }

  // Check account status and email verification if required
  if (user.accountStatus === 'inactive') {
    console.log(`[SIGNIN] Account ${user.userID} is inactive`);
    throw ErrorResponse(403, 'ACCOUNT_INACTIVE', 'Your account is not active');
  }

  if (emailVerificationRequired && !user.emailVerified) {
    console.log(`[SIGNIN] Account ${user.userID} email not verified and verification is required`);
    throw ErrorResponse(403, 'EMAIL_NOT_VERIFIED', 'Please verify your email address');
  }

  console.log(`[SIGNIN] All account requirements validated for ${user.userID}`);
}


/**
 * Checks login attempts and verifies password
 */
async function verifyCredentials(user: any, password: string, tableName?: string): Promise<void> {
  // Check login attempts
  const loginAttemptResult = await manageLoginAttempts(user.userID, false, tableName);
  if (loginAttemptResult.locked) {
    throw ErrorResponse(403, 'ACCOUNT_LOCKED', 
      'Your account has been locked due to too many failed login attempts');
  }

  // Then do password verification
  const isPasswordValid = await verifyPassword(password, user.password);
  if (!isPasswordValid) {
    await manageLoginAttempts(user.userID, false, tableName);  // Failed login attempt
    throw ErrorTypes.INVALID_CREDENTIALS();
  }

  // after successful password verification
  await manageLoginAttempts(user.userID, true, tableName);  // Successful login
}

/**
 * Calculates password expiry information
 */
function calculatePasswordExpiry(user: any): { expired: boolean, daysUntilExpiry: number } {
  const PASSWORD_EXPIRY_DAYS = 90;
  const PASSWORD_EXPIRY_MS = PASSWORD_EXPIRY_DAYS * 24 * 60 * 60 * 1000;

  // Calculate password age using lastPasswordChanged or fallback to createdAt
  const lastPasswordChange = user.lastPasswordChanged || user.createdAt;
  const passwordAge = Date.now() - (lastPasswordChange || user.createdAt);
  const passwordExpired = passwordAge > PASSWORD_EXPIRY_MS;

  // Calculate days until expiry, ensuring it's always a number
  let daysUntilExpiry = 0;
  if (!passwordExpired && !isNaN(passwordAge)) {
    daysUntilExpiry = Math.max(0, Math.floor((PASSWORD_EXPIRY_MS - passwordAge) / (24 * 60 * 60 * 1000)));
  }

  console.log('Password expiry calculation with user data:', {
    lastPasswordChanged: user.lastPasswordChanged,
    createdAt: user.createdAt,
    lastPasswordChange,
    passwordAge,
    passwordExpired,
    daysUntilExpiry,
    PASSWORD_EXPIRY_MS
  });

  return { expired: passwordExpired, daysUntilExpiry };
}

/**
 * Retrieves JWT secret based on account type
 */
async function getJwtSecret(user: any, tableName?: string): Promise<string | null> {
  let jwtSecret = null;
  if (user.accountType === 'child' && user.parentAccount && user.parentAccount !== 'ROOT') {
    // For child accounts - get their parent's JWT secret
    console.log(`[SIGN-IN] Fetching parent JWT secret for child account: ${user.userID}`);
    const parentUser = await getUserByPublicKey(user.parentAccount, tableName);
    jwtSecret = parentUser?.jwtSecret || null;
    if (jwtSecret) {
      console.log(`[SIGN-IN] Found parent JWT secret for child account: ${user.userID}`);
    } else {
      console.log(`[SIGN-IN] No parent JWT secret found for child account: ${user.userID}`);
    }
  } else if (user.accountType === 'parent') {
    // For parent accounts - return their own JWT secret
    jwtSecret = user.jwtSecret || null;
  }
  return jwtSecret;
}

/**
 * Prepares user data for response
 */
function prepareUserResponse(user: any): any {
  return {
    userID: user.userID,
    email: user.email,
    username: user.username,
    accountType: user.accountType,
    parentAccount: user.parentAccount,
    publicKey: user.publicKey,
    organizationName: user.organizationName || null,
    organizationUrl: user.organizationUrl || null,
    organizationUpdateCount: user.organizationUpdateCount || 0
  };
}

export const signInLambda = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  // Load configuration from the new service
  const config = await getConfig(event);
  
  console.log('[SIGNIN] Lambda invoked with headers:', {
    host: event.headers.Host || event.headers.host,
    origin: event.headers.Origin || event.headers.origin
  });
  
  console.log('[CONFIG] SignInLambda using configuration service', {
    environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
    frontendUrl: config.FRONTEND_URL,
    userTable: config.USER_DETAILS_TABLE_NAME
  });

  try {
    //Validate request body and extract fields
    const { email, password, parentPublicKey } = validateRequestBody(event.body);
    
    // Get user by email - we'll validate parent relationship in validateAccountRequirements
    console.log(`[SIGNIN] Looking up user by email: ${email}, parentPublicKey provided: ${!!parentPublicKey}`);

    let user;
    if (parentPublicKey) {
      // First try to find by email and parent (for regular child accounts)
      user = await getUserByEmailAndParent(email, parentPublicKey, config.USER_DETAILS_TABLE_NAME);

      if (!user) {
        // If not found, try by email only (could be promoted account)
        console.log(`[SIGNIN] User not found with email+parent, trying email only for potential promoted account`);
        user = await getUserByEmail(email, config.USER_DETAILS_TABLE_NAME);

        if (user) {
          console.log(`[SIGNIN] Found user by email only - accountType: ${user.accountType}, isPromoted: ${user.isPromotedAccount}`);
        }
      } else {
        console.log(`[SIGNIN] Found user by email+parent - accountType: ${user.accountType}`);
      }
    } else {
      // No parent public key provided - standard parent account lookup
      user = await getUserByEmail(email, config.USER_DETAILS_TABLE_NAME);
      if (user) {
        console.log(`[SIGNIN] Found user by email only - accountType: ${user.accountType}`);
      }
    }

    if (!user) {
      console.log(`[SIGNIN] User not found for email: ${email}`);
      throw ErrorTypes.USER_NOT_FOUND();
    }

    // Increment sign-in counter (non-fatal if it fails)
    try {
      await incrementSignInCount(user.userID, config.USER_DETAILS_TABLE_NAME);
    } catch (error) {
      // Just log the error but continue with sign-in process
      console.error('Error incrementing sign in count:', error);
    }

    // Validate account requirements
    await validateAccountRequirements(user, parentPublicKey, config.USER_DETAILS_TABLE_NAME);

    // Verify credentials and manage login attempts
    await verifyCredentials(user, password, config.USER_DETAILS_TABLE_NAME);

    // Calculate password expiry
    const passwordStatus = calculatePasswordExpiry(user);

    // Generate JWT token
    console.log(`[SIGN-IN] Generating token for user: ${user.userID}, account type: ${user.accountType}`);
    const token = await generateToken({
      userID: user.userID,
      email: user.email,
      accountType: user.accountType,
      publicKey: user.publicKey,
      parentAccount: user.parentAccount
    }, config.USER_DETAILS_TABLE_NAME);
    
    // Get JWT secret based on account type
    const jwtSecret = await getJwtSecret(user, config.USER_DETAILS_TABLE_NAME);

    // Prepare and return response
    return SuccessResponse(200, {
      token,
      user: prepareUserResponse(user),
      passwordStatus: {
        expired: passwordStatus.expired,
        daysUntilExpiry: isNaN(passwordStatus.daysUntilExpiry) ? 90 : passwordStatus.daysUntilExpiry
      },
      ...(jwtSecret && { jwtSecret })
    });
  } catch (error) {
    console.error('Error in signInLambda:', error);
    
    // For error handling, use config to determine if we should show detailed errors
    if (error && typeof error === 'object' && 'statusCode' in error && 'body' in error) {
      return error as APIGatewayProxyResult;
    }
    
    const errorMessage = config.NODE_ENV === 'local' 
      ? `${error instanceof Error ? error.message : 'An internal server error occurred'}` 
      : 'An internal server error occurred';
    
    return ErrorResponse(500, 'INTERNAL_SERVER_ERROR', errorMessage);
  }
};
 
