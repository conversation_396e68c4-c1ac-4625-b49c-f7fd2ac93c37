import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient, UpdateCommand } from "@aws-sdk/lib-dynamodb";
import { LoginAttemptResult, UserLoginAttempt } from './types/auth';

const client = new DynamoDBClient({
  region: process.env.AWS_REGION || 'eu-west-1'
});
const dynamoDB = DynamoDBDocumentClient.from(client);
const TABLE_NAME = process.env.USER_DETAILS_TABLE_NAME || 'userAuthentication';
const MAX_ATTEMPTS = 5;
const LOCK_DURATION = 15 * 60 * 1000; // 15 minutes

export { LoginAttemptResult, UserLoginAttempt };

export const manageLoginAttempts = async (userID: string, isSuccessful: boolean = false, tableName?: string): Promise<LoginAttemptResult> => {
  const targetTableName = tableName || TABLE_NAME;
  
  if (isSuccessful) {
    await dynamoDB.send(new UpdateCommand({
      TableName: targetTableName,
      Key: { userID },
      UpdateExpression: 'SET loginAttempts = :zero, lockedUntil = :nil',
      ExpressionAttributeValues: {
        ':zero': 0,
        ':nil': null
      }
    }));
    return { locked: false, attempts: 0 };
  }

  const params = {
    TableName: targetTableName,
    Key: { userID },
    UpdateExpression: `
      SET loginAttempts = if_not_exists(loginAttempts, :zero) + :inc,
      lastLoginAttempt = :now,
      lockedUntil = if_not_exists(lockedUntil, :nil)
    `,
    ExpressionAttributeValues: {
      ':inc': 1,
      ':zero': 0,
      ':now': Date.now(),
      ':nil': null
    },
    ReturnValues: 'ALL_NEW' as const
  };

  const result = await dynamoDB.send(new UpdateCommand(params));
  const user = result.Attributes as UserLoginAttempt;

  if (!user) {
    return { locked: false, attempts: 0 };
  }

  if (user.loginAttempts >= MAX_ATTEMPTS) {
    await dynamoDB.send(new UpdateCommand({
      TableName: targetTableName,
      Key: { userID },
      UpdateExpression: 'SET lockedUntil = :lockTime',
      ExpressionAttributeValues: {
        ':lockTime': Date.now() + LOCK_DURATION
      }
    }));

    return { locked: true, attempts: user.loginAttempts };
  }

  return { locked: false, attempts: user.loginAttempts };
};

const ttlParams = {
  TableName: 'userAuthentication',
  TimeToLiveSpecification: {
    AttributeName: 'lockedUntil',
    Enabled: true
  }
};
