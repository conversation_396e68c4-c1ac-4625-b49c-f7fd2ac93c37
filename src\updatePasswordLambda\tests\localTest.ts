import { updatePasswordLambda } from '../handler';
import { APIGatewayProxyEvent } from 'aws-lambda';
import { createMockEvent, createTestUser } from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { generateResetToken } from '../../shared/utils/tokenUtils';
import { getTestUser } from '../../shared/testUtils/databaseTestUtils';
import { Environment } from '../../shared/services/environment';

interface TestUser {
  userID: string;
  username: string;
  email: string;
  password: string;
  emailVerified: boolean;
  accountStatus: string;
  createdAt: number;
  verificationToken: string;
  verificationTokenExpiry: number;
  lastVerificationSentAt: number;
  publicKey: string;
  resetPasswordOTP?: string;
  resetPasswordOTPExpiry?: number;
  lastResetPasswordRequestAt?: number;
  parentAccount?: string | null;
}

async function runLocalTest() {
  process.env.ENCRYPTION_KEY = "dBG9kF7bH5cJ2mN8pQ4sT6wX0yZ3vA1eI/nL+uM9oR4=";
  console.log('Running local test for Update Password Lambda');
  
  try {
    await clearTestData();
    
    // Create a test user with valid reset password fields
    const testUser = await createTestUser({
      emailVerified: true,
      resetPasswordOTP: '654321',
      resetPasswordOTPExpiry: Date.now() + 3600000, // Valid for 1 hour
      lastResetPasswordRequestAt: Date.now() - 300000, // 5 minutes ago
      accountType: 'parent',
      parentAccount: 'ROOT'
    });
    
    await seedTestUser(testUser);
    console.log('Test user created:', testUser.email);

    // Generate valid reset token
    if (!testUser.resetPasswordOTP) {
      throw new Error('Reset password OTP is not set');
    }
    const resetToken = generateResetToken(testUser.email, testUser.resetPasswordOTP, 
      testUser.accountType === 'child' ? testUser.parentAccount : undefined);
    
    // Create event with valid token and password
    const event = createMockEvent({
      token: resetToken,
      newPassword: 'NewStrongP@ssw0rd123'
    }, '/update-password');
    
    const result = await updatePasswordLambda(event as APIGatewayProxyEvent);
    console.log('Result:', result);

    if (result.statusCode === 200) {
      const updatedUser = await getTestUser(testUser.userID);
      if (!updatedUser) {
        throw new Error('Updated user not found');
      }
      console.log('Password updated successfully');
      console.log('Reset password fields cleared:', {
        resetPasswordOTP: updatedUser.resetPasswordOTP,
        resetPasswordOTPExpiry: updatedUser.resetPasswordOTPExpiry,
        lastResetPasswordRequestAt: updatedUser.lastResetPasswordRequestAt
      });
    }

    await clearTestData();
  } catch (error) {
    console.error('Error:', error);
  } finally {
    delete process.env.ENCRYPTION_KEY;
  }
}

runLocalTest().catch(console.error); 