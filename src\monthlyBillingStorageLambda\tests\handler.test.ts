import { calculateCostsForUser } from '../../shared/utils/costCalculator';
import { getBillingRecord } from '../../shared/testUtils/billingTestUtils';
import { monthlyBillingStorageLambda } from '../handler';
import { seedTestUser, clearTestData } from '../../shared/testUtils/databaseLifecycle';
import { createTestUser } from '../../shared/testUtils/lambdaTestUtils';
import { updateParentAccountBalances } from '../../shared/database/balanceOperations';
import { checkAndResetMonthlyOperations } from '../../shared/database/userOperations';
import { storeMonthlyBillingRecord } from '../../shared/database/billingOperations';
import { DynamoDB } from 'aws-sdk';

jest.mock('../../shared/utils/costCalculator', () => ({
  calculateCostsForUser: jest.fn()
}));

describe('Monthly Billing Storage Lambda', () => {
    beforeEach(async () => {
        jest.clearAllMocks();
        process.env.BILLING_TABLE_NAME = 'authiqaBilling';
        process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
        process.env.INTERNAL_SERVICE_KEY = 'test-internal-key';
        await clearTestData();
    });

    afterEach(() => {
        delete process.env.BILLING_TABLE_NAME;
        delete process.env.USER_DETAILS_TABLE_NAME;
        delete process.env.INTERNAL_SERVICE_KEY;
    });

    it.skip('should store monthly billing successfully', async () => {
        // Create a test parent account
        const parentUser = await createTestUser({
            accountType: 'parent',
            emailVerified: true
        });
        await seedTestUser(parentUser);

        const mockCosts = {
            totalAccounts: 5,
            accountsCost: 0.15,
            totalOperations: 1000,
            ioCost: 0.25,
            totalCost: 0.40
        };

        (calculateCostsForUser as jest.Mock).mockResolvedValue(mockCosts);

        const currentMonth = new Date().toISOString().slice(0, 7);

        // Execute the lambda
        await monthlyBillingStorageLambda();

        // Verify the stored record
        const storedRecord = await getBillingRecord(parentUser.publicKey, currentMonth);
        expect(storedRecord).toMatchObject({
            publicKey: parentUser.publicKey,
            monthYear: currentMonth,
            totalAccounts: mockCosts.totalAccounts,
            costAssociatedWithAccounts: mockCosts.accountsCost,
            totalIOInteractions: mockCosts.totalOperations,
            costAssociatedWithIO: mockCosts.ioCost,
            totalFinalCost: mockCosts.totalCost
        });
    });

    it.skip('should handle cost calculation failure', async () => {
        // Create a test parent account
        const parentUser = await createTestUser({
            accountType: 'parent',
            emailVerified: true
        });
        await seedTestUser(parentUser);

        (calculateCostsForUser as jest.Mock).mockRejectedValue(new Error('Cost calculation failed'));

        // The lambda should complete but log the error
        await monthlyBillingStorageLambda();
        
        // Verify no billing record was created
        const currentMonth = new Date().toISOString().slice(0, 7);
        const storedRecord = await getBillingRecord(parentUser.publicKey, currentMonth);
        expect(storedRecord).toBeNull();
    });

    it.skip('should process multiple parent accounts', async () => {
        // Create multiple test parent accounts
        const parent1 = await createTestUser({
            accountType: 'parent',
            emailVerified: true
        });
        const parent2 = await createTestUser({
            accountType: 'parent',
            emailVerified: true
        });

        await seedTestUser(parent1);
        await seedTestUser(parent2);

        const mockCosts = {
            totalAccounts: 5,
            accountsCost: 0.15,
            totalOperations: 1000,
            ioCost: 0.25,
            totalCost: 0.40
        };

        (calculateCostsForUser as jest.Mock).mockResolvedValue(mockCosts);

        await monthlyBillingStorageLambda();

        const currentMonth = new Date().toISOString().slice(0, 7);
        
        // Verify both parents have billing records
        const record1 = await getBillingRecord(parent1.publicKey, currentMonth);
        const record2 = await getBillingRecord(parent2.publicKey, currentMonth);
        
        expect(record1).toBeTruthy();
        expect(record2).toBeTruthy();
    });
});