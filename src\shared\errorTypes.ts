import { APIGatewayProxyResult } from 'aws-lambda';
import { ErrorResponse } from './responseUtils';

export const ErrorTypes = {
  MISSING_REQUEST_BODY: () => ErrorResponse(400, 'MISSING_REQUEST_BODY', 'Request body is required'),
  INVALID_REQUEST_BODY: () => ErrorResponse(400, 'INVALID_REQUEST_BODY', 'Invalid JSON in request body'),
  MISSING_REQUIRED_FIELDS: () => ErrorResponse(400, 'MISSING_REQUIRED_FIELDS', 'Required fields are missing'),
  INVALID_CREDENTIALS: () => ErrorResponse(401, 'INVALID_CREDENTIALS', 'Invalid email or password'),
  EMAIL_NOT_VERIFIED: () => ErrorResponse(403, 'EMAIL_NOT_VERIFIED', 'Please verify your email address'),
  ACCOUNT_INACTIVE: () => ErrorResponse(403, 'ACCOUNT_INACTIVE', 'Your account is not active'),
  ACCOUNT_LOCKED: () => ErrorResponse(403, 'ACCOUNT_LOCKED', 'Your account has been locked due to too many failed login attempts'),
  USER_NOT_FOUND: () => {
    console.log('[ErrorTypes] Creating USER_NOT_FOUND error response');
    const response = ErrorResponse(404, 'USER_NOT_FOUND', 'User not found');
    console.log('[ErrorTypes] Generated response:', JSON.stringify(response, null, 2));
    return response;
  },
  EMAIL_ALREADY_VERIFIED: () => ErrorResponse(400, 'EMAIL_ALREADY_VERIFIED', 'Email is already verified'),
  INVALID_OTP: () => ErrorResponse(400, 'INVALID_OTP', 'Invalid OTP provided'),
  OTP_EXPIRED: () => ErrorResponse(400, 'OTP_EXPIRED', 'OTP has expired'),
  INTERNAL_ERROR: () => ErrorResponse(500, 'INTERNAL_SERVER_ERROR', 'An internal server error occurred'),
  INVALID_OTP_FORMAT: (): APIGatewayProxyResult => 
    ErrorResponse(400, 'INVALID_OTP_FORMAT', 'OTP must be 6 digits'),
  RATE_LIMIT_EXCEEDED: (message = 'Too many requests. Please try again later') => 
    ErrorResponse(429, 'RATE_LIMIT_EXCEEDED', message),
  INVALID_EMAIL_FORMAT: () => 
    ErrorResponse(400, 'INVALID_EMAIL_FORMAT', 'Invalid email format'),
  EMAIL_SENDING_FAILED: () => 
    ErrorResponse(500, 'EMAIL_SENDING_FAILED', 'Failed to send verification email'),
  RESET_PASSWORD: {
    ACCOUNT_INACTIVE: () => 
      ErrorResponse(403, 'ACCOUNT_INACTIVE', 'Password reset not allowed for inactive account'),
    
    ACCOUNT_LOCKED: () => 
      ErrorResponse(403, 'ACCOUNT_LOCKED', 'Password reset not allowed for locked account'),
    
    EMAIL_NOT_VERIFIED: () => 
      ErrorResponse(403, 'EMAIL_NOT_VERIFIED', 'Email must be verified before requesting password reset'),
    
    RATE_LIMIT_EXCEEDED: () => 
      ErrorResponse(429, 'RATE_LIMIT_EXCEEDED', 'Please wait before requesting another reset link'),
  },
  UPDATE_PASSWORD: {
    TOKEN_NOT_PROVIDED: () => 
      ErrorResponse(400, 'TOKEN_NOT_PROVIDED', 'Reset token is required'),
    
    INVALID_TOKEN: () => 
      ErrorResponse(400, 'INVALID_TOKEN', 'Invalid or malformed reset token'),
    
    EXPIRED_TOKEN: () => 
      ErrorResponse(400, 'EXPIRED_TOKEN', 'Reset token has expired'),
    
    INVALID_PASSWORD: () => 
      ErrorResponse(400, 'INVALID_PASSWORD_FORMAT', 
        'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    
    OTP_MISMATCH: () => 
      ErrorResponse(400, 'INVALID_OTP', 'Invalid reset code'),
    
    OTP_EXPIRED: () => 
      ErrorResponse(400, 'OTP_EXPIRED', 'Reset code has expired'),
    MISSING_PARENT_PUBLIC_KEY: () => ({
      statusCode: 400,
      code: 'MISSING_PARENT_PUBLIC_KEY',
      message: 'Parent PUBLIC key is required for child accounts'
    }),
    INVALID_PARENT_PUBLIC_KEY: () => ({
      statusCode: 401,
      code: 'INVALID_PARENT_PUBLIC_KEY',
      message: 'Invalid parent PUBLIC key'
    })
  },
  EMAIL_VERIFICATION: {
    TOKEN_NOT_PROVIDED: () => 
      ErrorResponse(400, 'TOKEN_NOT_PROVIDED', 'Verification token is required'),
    
    INVALID_TOKEN: () => 
      ErrorResponse(400, 'INVALID_TOKEN', 'Invalid verification token'),
    
    TOKEN_EXPIRED: () => 
      ErrorResponse(400, 'TOKEN_EXPIRED', 'Verification token has expired'),
    
    EMAIL_ALREADY_VERIFIED: () => 
      ErrorResponse(400, 'EMAIL_ALREADY_VERIFIED', 'Email is already verified'),
    
    USER_NOT_FOUND: () => 
      ErrorResponse(404, 'USER_NOT_FOUND', 'User not found'),
    
    INVALID_FORMAT: () => 
      ErrorResponse(
        400, 
        'INVALID_TOKEN_FORMAT', 
        'Verification token must be 12 characters long and contain only uppercase letters and numbers'
      ),
    
    EXPIRED: () => 
      ErrorResponse(400, 'TOKEN_EXPIRED', 'This verification link has expired. Please request a new one.')
  },
  INVALID_TOKEN_FORMAT: () => 
    ErrorResponse(400, 'INVALID_TOKEN_FORMAT', 'Verification token must be 12 alphanumeric characters'),
  ORGANIZATION: {
    INVALID_ORGANIZATION_NAME: (message: string = 'Invalid organization name') => 
      ErrorResponse(400, 'INVALID_ORGANIZATION_NAME', message),
    
    INVALID_ORGANIZATION_URL: (message: string = 'Invalid organization URL') => 
      ErrorResponse(400, 'INVALID_ORGANIZATION_URL', message),
    
    UPDATE_LIMIT_REACHED: () => 
      ErrorResponse(403, 'ORGANIZATION_UPDATE_LIMIT', 
      'Organization details can only be updated once'),
    
    UNAUTHORIZED_ACCESS: () => 
      ErrorResponse(401, 'UNAUTHORIZED_ACCESS', 
      'Only parent accounts can update organization details')
  },
  UNAUTHORIZED: () => ErrorResponse(401, 'UNAUTHORIZED', 'Invalid or missing authorization token'),
  DUPLICATE_EMAIL_USERNAME_COMBO: () => 
    ErrorResponse(409, 'DUPLICATE_EMAIL_USERNAME_COMBO', 'A user with the same email and username already exists under this parent account'),
  INVALID_PARENT_ACCOUNT: () => ErrorResponse(400, 'INVALID_PARENT_ACCOUNT', 'Invalid parent account'),
  PARENT_ACCOUNT_NOT_FOUND: () => ErrorResponse(404, 'PARENT_ACCOUNT_NOT_FOUND', 'Parent account not found'),
  MISSING_PARENT_PUBLIC_KEY: () => ErrorResponse(400, 'MISSING_PARENT_PUBLIC_KEY', 'PUBLIC key is required'),
  INVALID_PARENT_PUBLIC_KEY: 'INVALID_PARENT_PUBLIC_KEY',
  INVALID_AUTH_URLS: (message: string = 'Invalid authentication URLs') => 
    ErrorResponse(400, 'INVALID_AUTH_URLS', message)
};