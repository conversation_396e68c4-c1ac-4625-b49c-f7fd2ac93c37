import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { getBillingHistory } from '../shared/database/billingOperations';
import { ErrorResponse, SuccessResponse } from '../shared/responseUtils';
import { verifyToken } from '../shared/utils/tokenUtils';
import { validateDateFormat } from '../shared/utils/dateUtils';

export const billingHistoryLambda = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    // Authorization Check
    const authHeader = event.headers.Authorization || event.headers.authorization;
    if (!authHeader) {
      return ErrorResponse(401, 'UNAUTHORIZED', 'Missing authorization token');
    }
    const token = authHeader.replace('Bearer ', '');
    const decodedToken = await verifyToken(token);
    if (!decodedToken || !decodedToken.accountType || decodedToken.accountType !== 'parent' || !decodedToken.publicKey) {
      return ErrorResponse(401, 'UNAUTHORIZED', 'Only parent accounts can access billing history');
    }
    const { startDate, endDate, limit } = event.queryStringParameters || {};
    // Validate date formats
    if (startDate && !validateDateFormat(startDate)) {
      return ErrorResponse(400, 'INVALID_DATE_FORMAT', 'Start date must be in YYYY-MM format');
    }
    if (endDate && !validateDateFormat(endDate)) {
      return ErrorResponse(400, 'INVALID_DATE_FORMAT', 'End date must be in YYYY-MM format');
    }
    
    // Validate limit
    const limitNum = limit ? parseInt(limit) : undefined;
    if (limit && isNaN(limitNum!)) {
      return ErrorResponse(400, 'INVALID_LIMIT', 'Limit must be a valid number');
    }
    if (startDate && endDate) {
      const startTimestamp = new Date(startDate + '-01').getTime();
      const endTimestamp = new Date(endDate + '-01').getTime();
      if (startTimestamp > endTimestamp) {
        return ErrorResponse(400, 'INVALID_DATE_RANGE', 'End date cannot be before start date');
      }
    }
    const billingRecords = await getBillingHistory(decodedToken.publicKey, startDate, endDate, limitNum);
    // Calculate summary
    const totalCost = billingRecords.reduce((sum, record) => sum + record.totalFinalCost, 0);
    const averageMonthlySpend = billingRecords.length > 0 ? totalCost / billingRecords.length : 0;
    return SuccessResponse(200, {
        billingRecords,
        summary: {
            totalCost,
            averageMonthlySpend
        }
    });
  } catch (error) {
    console.error('Error fetching billing history:', error);
    return ErrorResponse(500, 'INTERNAL_ERROR', 'An error occurred while fetching billing history');
  }
};  