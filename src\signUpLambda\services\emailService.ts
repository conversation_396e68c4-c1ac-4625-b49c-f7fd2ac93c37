import { sendVerificationEmail } from '../../shared/emailService';
import { APIGatewayProxyEvent } from 'aws-lambda';

export class EmailService {
  static async sendSignupVerification(
    email: string,
    verificationToken: string,
    accountType: 'parent' | 'child',
    parentPub<PERSON><PERSON>ey?: string,
    verifyAuthPath?: string,
    event?: APIGatewayProxyEvent
  ) {
    try {
      // Make sure we're passing the event parameter to the shared email service
      await sendVerificationEmail(
        email,
        verificationToken,
        accountType,
        parentPublicKey,
        verifyAuthPath,
        event
      );
      
      return {
        success: true
      };
    } catch (error) {
      console.error('Error sending verification email:', error);
      return {
        success: false,
        error: {
          statusCode: 500,
          body: JSON.stringify({
            error: 'EMAIL_SENDING_FAILED',
            message: 'Failed to send verification email'
          })
        }
      };
    }
  }
}
