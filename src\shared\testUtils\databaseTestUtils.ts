import { 
  <PERSON><PERSON><PERSON>om<PERSON>, 
  <PERSON>ete<PERSON>ommand, 
  <PERSON><PERSON><PERSON><PERSON>,
  GetCommand,
  UpdateCommand
} from "@aws-sdk/lib-dynamodb";
import { TABLE_NAME } from '../database';
import * as bcrypt from 'bcryptjs';
import { CreateTableCommand, DeleteTableCommand, DescribeTableCommand } from "@aws-sdk/client-dynamodb";
import { USER_TABLE_CONFIG } from '../database/tableConfig';
import { dynamoDB, dynamoDBClient } from '../database';

export async function clearTestDatabase(): Promise<void> {
  const result = await dynamoDBClient.send(new ScanCommand({
    TableName: TABLE_NAME
  }));

  if (result.Items) {
    for (const item of result.Items) {
      await dynamoDBClient.send(new DeleteCommand({
        TableName: TABLE_NAME,
        Key: { userID: item.userID }
      }));
    }
  }
};

export const getTestUser = async (userID: string) => {
  const result = await dynamoDBClient.send(new GetCommand({
    TableName: TABLE_NAME,
    Key: { userID }
  }));
  return result.Item;
};

export const updateTestUser = async (userID: string, updates: Partial<{
  emailVerified: boolean;
  OTP: string | null;
  OTPExpiry: number | null;
  lastOTPSentAt: number | null;
  lastResetDate: number;
}>) => {
  const updateExpressions: string[] = [];
  const expressionAttributeValues: Record<string, any> = {};

  Object.entries(updates).forEach(([key, value]) => {
    updateExpressions.push(`#${key} = :${key}`);
    expressionAttributeValues[`:${key}`] = value;
  });

  const expressionAttributeNames = Object.fromEntries(
    Object.keys(updates).map(key => [`#${key}`, key])
  );

  await dynamoDBClient.send(new UpdateCommand({
    TableName: TABLE_NAME,
    Key: { userID },
    UpdateExpression: `SET ${updateExpressions.join(', ')}`,
    ExpressionAttributeNames: expressionAttributeNames,
    ExpressionAttributeValues: expressionAttributeValues
  }));
};

async function waitForTableStatus(tableName: string, desiredStatus: string): Promise<void> {
  console.log(`Waiting for table ${tableName} to be ${desiredStatus}...`);
  while (true) {
    try {
      const { Table } = await dynamoDBClient.send(new DescribeTableCommand({ TableName: tableName }));
      if (Table?.TableStatus === desiredStatus) {
        console.log(`Table ${tableName} is now ${desiredStatus}`);
        
        // Additional check for GSI status
        const allIndexesActive = Table.GlobalSecondaryIndexes?.every(
          gsi => gsi.IndexStatus === desiredStatus
        );
        
        if (allIndexesActive) {
          console.log('All GSIs are active');
          break;
        }
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error: any) {
      if (error.name === 'ResourceNotFoundException' && desiredStatus === 'DELETED') {
        break;
      }
      throw error;
    }
  }
}

export async function createTestTable(): Promise<void> {
  const tableName = USER_TABLE_CONFIG.TableName!;
  
  try {
    console.log('Starting test table creation process...');
    
    // Try to delete existing table
    try {
      console.log('Attempting to delete existing table...');
      await dynamoDBClient.send(new DeleteTableCommand({ TableName: tableName }));
      await waitForTableStatus(tableName, 'DELETED');
    } catch (error: any) {
      if (error.name !== 'ResourceNotFoundException') {
        throw error;
      }
    }

    // Create new table
    console.log('Creating new table with indexes...');
    await dynamoDBClient.send(new CreateTableCommand(USER_TABLE_CONFIG));
    
    // Wait for table and indexes to be active
    await waitForTableStatus(tableName, 'ACTIVE');
    
    // Verify indexes
    const { Table } = await dynamoDBClient.send(new DescribeTableCommand({ TableName: tableName }));
    const indexNames = Table?.GlobalSecondaryIndexes?.map(idx => idx.IndexName);
    console.log('Created indexes:', indexNames);
    
    const requiredIndexes = ['emailIndex', 'otpIndex', 'usernameIndex', 'accountStatusIndex', 'parentAccountComboIndex'];
    const missingIndexes = requiredIndexes.filter(idx => !indexNames?.includes(idx));
    
    if (missingIndexes.length > 0) {
      throw new Error(`Missing required indexes: ${missingIndexes.join(', ')}`);
    }

    console.log('Test table creation completed successfully');
  } catch (error) {
    console.error('Error creating test table:', error);
    throw error;
  }
}
