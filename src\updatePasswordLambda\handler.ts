import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ErrorTypes } from '../shared/errorTypes';
import { addCorsHeaders } from '../shared/corsHandler';
import { decryptResetToken } from '../shared/utils/tokenUtils';
import { getUserByEmail, getUserByEmailAndParent } from '../shared/database/userOperations';
import { validatePassword } from '../shared/validation/userValidation';
import { hashPassword } from '../shared/auth/passwordUtils';
import { updateUserPassword } from '../shared/database/userOperations';
import { SuccessResponse } from '../shared/responseUtils';
import { ErrorResponse } from '../shared/responseUtils';
import { incrementPasswordUpdateCount } from '../shared/database/userOperations';
import { getConfig } from '../shared/services/configService';


export const updatePasswordLambda = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  // Load configuration from the new service
  const config = await getConfig(event);
  
  console.log('[UPDATE-PASSWORD] Lambda invoked with headers:', {
    host: event.headers.Host || event.headers.host,
    origin: event.headers.Origin || event.headers.origin
  });
  
  console.log('[CONFIG] UpdatePasswordLambda using configuration service', {
    environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
    frontendUrl: config.FRONTEND_URL,
    userTable: config.USER_DETAILS_TABLE_NAME
  });

  try {
    if (!event.body) {
      return addCorsHeaders(ErrorTypes.MISSING_REQUEST_BODY(), event);
    }

    let requestBody;
    try {
      requestBody = JSON.parse(event.body);
    } catch {
      return addCorsHeaders(ErrorTypes.INVALID_REQUEST_BODY(), event);
    }

    const { token, password } = requestBody;
    console.log('Update Password Request received with token:', {
      hasToken: !!token,
      tokenLength: token ? token.length : 0
    });

    if (!token || !password) {
      return addCorsHeaders(ErrorTypes.UPDATE_PASSWORD.TOKEN_NOT_PROVIDED(), event);
    }

    // Validate password format (pattern from signUpLambda)
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      return addCorsHeaders(ErrorTypes.UPDATE_PASSWORD.INVALID_PASSWORD(), event);
    }

    // Decrypt and validate token
    let decodedToken;
    try {
      decodedToken = decryptResetToken(token);
      console.log('Token decryption successful:', {
        email: decodedToken.email,
        hasParentPublicKey: !!decodedToken.parentPublicKey,
        hasOTP: !!decodedToken.otp
      });
    } catch (error) {
      console.error('Token decryption failed: ', error);
      return addCorsHeaders(ErrorTypes.UPDATE_PASSWORD.INVALID_TOKEN(), event);
    }

    // Get user with the correct parent API key if it's provided in the token
    let user;
    if (decodedToken.parentPublicKey) {
      // For child accounts, use getUserByEmailAndParent
      user = await getUserByEmailAndParent(decodedToken.email, decodedToken.parentPublicKey, config.USER_DETAILS_TABLE_NAME);
    } else {
      // For parent accounts, use getUserByEmail
      user = await getUserByEmail(decodedToken.email, config.USER_DETAILS_TABLE_NAME);
    }

    if (!user) {
      return addCorsHeaders(ErrorTypes.USER_NOT_FOUND(), event);
    }

    // Validate account type
    if (user.accountType === 'child') {
      if (!decodedToken.parentPublicKey) {
        return addCorsHeaders(ErrorResponse(400, 'MISSING_PARENT_PUBLIC_KEY', 'Parent public key is required for child accounts'), event);
      }
    } else {
      // For parent accounts, verify it's a root parent account
      if (user.parentAccount !== 'ROOT') {
        return addCorsHeaders(ErrorResponse(401, 'INVALID_CREDENTIALS', 'Invalid credentials'), event);
      }
    }

    // Check OTP
    if (!user.resetPasswordOTP || user.resetPasswordOTP !== decodedToken.otp) {
      return addCorsHeaders(ErrorTypes.UPDATE_PASSWORD.OTP_MISMATCH(), event);
    }

    if (!user.resetPasswordOTPExpiry || user.resetPasswordOTPExpiry < Date.now()) {
      return addCorsHeaders(ErrorTypes.UPDATE_PASSWORD.OTP_EXPIRED(), event);
    }

    // Hash new password
    const hashedPassword = await hashPassword(password);

    // Increment counter just before the actual update
    await incrementPasswordUpdateCount(user.userID, config.USER_DETAILS_TABLE_NAME);

    // Update password with timestamp
    await updateUserPassword(user.userID, hashedPassword, Date.now(), config.USER_DETAILS_TABLE_NAME);
    
    return addCorsHeaders(SuccessResponse(200, {
      message: 'Password updated successfully'
    }), event);
  } catch (error) {
    console.error('Error updating password:', error);
    
    // If the error is already an APIGatewayProxyResult (from ErrorTypes)
    if (error && typeof error === 'object' && 'statusCode' in error && 'body' in error) {
      return addCorsHeaders(error as APIGatewayProxyResult, event);
    }
    
    
    // Use config to determine if we should show detailed errors
    if (config.NODE_ENV === 'local') {
      const detailedMessage = error instanceof Error ? error.message : 'An internal server error occurred';
      return addCorsHeaders(ErrorResponse(500, 'INTERNAL_SERVER_ERROR', detailedMessage), event);
    } else {
      return addCorsHeaders(ErrorTypes.INTERNAL_ERROR(), event);
    }
  }
};      

 
