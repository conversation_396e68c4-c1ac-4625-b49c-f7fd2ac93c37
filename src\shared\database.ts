import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient } from "@aws-sdk/lib-dynamodb";
import * as dotenv from 'dotenv';
import { DB_CONFIG } from './config';

dotenv.config();

// Create the base DynamoDB client
const client = new DynamoDBClient({
  region: process.env.AWS_REGION || 'eu-west-1',
  ...(process.env.AWS_ACCESS_KEY_ID && {
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!
    }
  })
});

// Create the DynamoDB Document client
export const dynamoDB = DynamoDBDocumentClient.from(client);

// Export the base client for operations that need it (like creating tables)
export const dynamoDBClient = client;

// Export the table name constant
export const TABLE_NAME = DB_CONFIG.TABLE_NAME;
