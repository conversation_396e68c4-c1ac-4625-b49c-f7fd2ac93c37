name: Deploy <PERSON> on AWS - Project (Authiqa)

on:
  push:
    branches:
      - main
      - staging
    paths:
      - "src/signUpLambda/**"
      - "src/signInLambda/**"
      - "src/emailConfirmationLambda/**"
      - "src/resendConfirmationEmailLambda/**"
      - "src/resetPasswordLambda/**"
      - "src/updatePasswordLambda/**"
      - "src/updateOrganizationLambda/**"
      - "src/getOrganizationDetailsLambda/**"
      - "src/getChildAccountsLambda/**"
      - "src/costCalculatorLambda/**"
      - "src/billingHistoryLambda/**"
      - "src/monthlyBillingStorageLambda/**"
      - "src/stripeWebhookLambda/**"
      - "src/paymentHistoryLambda/**"
      - "src/paymentInitializationLambda/**"
      - "src/promoteAccountLambda/**"
      - "src/googleAuthLambda/**"
      - "src/githubAuthLambda/**"
      - "src/getUserProfileLambda/**"
      - "src/shared/**"

env:
  REPO_NAME: "SignInSignUp"
  S3_BUCKET_PREFIX: "natuvea-lambda-deployments"
  STAGING_S3_BUCKET_PREFIX: "staging-natuvea-lambda-deployments"
  AWS_REGIONS: "eu-west-1"
  ENCRYPTION_KEY: ${{ secrets.ENCRYPTION_KEY }}

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v2
        with:
          fetch-depth: 0
          persist-credentials: true
          token: ${{ secrets.GH_TOKEN }}

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "20"

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGIONS }}

      - name: Install Dependencies
        run: npm ci

      - name: Compile TypeScript
        run: npm install typescript --save-dev

      - name: Install Yup
        run: npm install yup --save

      - name: Run Tests with Coverage
        run: npm run test:coverage

      - name: Package Lambdas
        run: |
          chmod +x package_lambdas.sh
          npm run package

      - name: Upload to S3 and Update Lambda from S3
        run: |
          set -euo pipefail

          declare -A lambdas=(
            ["signUpLambda"]="signUpLambda"
            ["signInLambda"]="signInLambda"
            ["emailConfirmationLambda"]="emailConfirmationLambda"
            ["resendConfirmationEmailLambda"]="resendConfirmationEmailLambda"
            ["resetPasswordLambda"]="resetPasswordLambda"
            ["updatePasswordLambda"]="updatePasswordLambda"
            ["updateOrganizationLambda"]="updateOrganizationLambda"
            ["getOrganizationDetailsLambda"]="getOrganizationDetailsLambda"
            ["getChildAccountsLambda"]="getChildAccountsLambda"
            ["costCalculatorLambda"]="costCalculatorLambda"
            ["billingHistoryLambda"]="billingHistoryLambda"
            ["monthlyBillingStorageLambda"]="monthlyBillingStorageLambda"
            ["stripeWebhookLambda"]="stripeWebhookLambda"
            ["paymentHistoryLambda"]="paymentHistoryLambda"
            ["paymentInitializationLambda"]="paymentInitializationLambda"
            ["promoteAccountLambda"]="promoteAccountLambda"
            ["googleAuthLambda"]="googleAuthLambda"
            ["githubAuthLambda"]="githubAuthLambda"
            ["getUserProfileLambda"]="getUserProfileLambda"
          )

          CURRENT_BRANCH="${GITHUB_REF##*/}"
          echo "🔀 Branch: $CURRENT_BRANCH"

          for region in $AWS_REGIONS; do
            if [[ "$CURRENT_BRANCH" == "staging" ]]; then
              S3_BUCKET="${STAGING_S3_BUCKET_PREFIX}"
              SUFFIX="-staging"
            else
              S3_BUCKET="${S3_BUCKET_PREFIX}"
              SUFFIX=""
            fi

            for lambda in "${!lambdas[@]}"; do
              BASE_NAME="${lambdas[$lambda]}"
              LAMBDA_NAME="${BASE_NAME}${SUFFIX}"
              ZIP_FILE="zips/${BASE_NAME}-package.zip"
              S3_KEY="${LAMBDA_NAME}/${BASE_NAME}-package.zip"

              if [[ -f "$ZIP_FILE" ]]; then
                echo "🚀 Uploading and Deploying $LAMBDA_NAME..."

                aws s3 cp "$ZIP_FILE" "s3://${S3_BUCKET}/${S3_KEY}" --region "$region"

                aws lambda update-function-code \
                  --function-name "$LAMBDA_NAME" \
                  --s3-bucket "$S3_BUCKET" \
                  --s3-key "$S3_KEY" \
                  --region "$region" || echo "❌ Failed to update $LAMBDA_NAME"
              else
                echo "⏭ Zip not found for $BASE_NAME, skipping."
              fi
            done
          done

      - name: Generate Coverage Badges
        run: |
          mkdir -p badges
          npm run make:badges

      - name: Commit Coverage Badges
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
        run: |
          git config --local user.email "github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git add badges/
          git commit -m "chore: update coverage badges" || echo "⚠️ No changes to commit"
          git push || echo "⚠️ Push failed (likely no changes)"
