/**
 * Organization configuration for widget authentication
 * Controls which organizations can promote accounts and use widget authentication
 */

// List of organizations authorized for account promotion and widget authentication
export const SPECIAL_ORGANIZATIONS = [
  'natuvea',           // Your organization name
  // Add more organizations here if needed in the future
];

/**
 * Checks if an organization is authorized for special widget authentication privileges
 * @param orgName - Organization name to check
 * @returns true if organization is authorized, false otherwise
 */
export const isSpecialOrganization = (orgName: string): boolean => {
  if (!orgName) {
    console.log('[ORG-CONFIG] No organization name provided');
    return false;
  }
  
  const normalizedOrgName = orgName.toLowerCase().trim();
  const isSpecial = SPECIAL_ORGANIZATIONS.includes(normalizedOrgName);
  
  console.log(`[ORG-CONFIG] Checking organization "${orgName}" (normalized: "${normalizedOrgName}") - Special: ${isSpecial}`);
  
  return isSpecial;
};

/**
 * Gets the list of all special organizations (for logging/debugging)
 * @returns Array of special organization names
 */
export const getSpecialOrganizations = (): string[] => {
  return [...SPECIAL_ORGANIZATIONS];
};
