import { emailConfirmationLambda } from '../handler';
import { APIGatewayProxyEvent, Context, Callback, APIGatewayProxyResult } from 'aws-lambda';
import { createMockEvent, createMockContext, createTestUser } from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, seedTestUser } from '../../shared/testUtils/databaseLifecycle';

async function runLocalTest() {
  console.log('Running local test for Email Confirmation Lambda');
  
  try {
    await clearTestData();
    
    const testUser = await createTestUser({
      emailVerified: false,
      verificationToken: 'ABC123XYZ789',
      verificationTokenExpiry: Date.now() + 900000
    });
    
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email,
      OTP: '123456'
    }, '/confirm-email');
    
    const context = createMockContext();
    const callback: Callback<APIGatewayProxyResult> = (error, result) => {};

    const result = await emailConfirmationLambda(
      event as APIGatewayProxyEvent, 
   
    );
    console.log('Result:', result);

    await clearTestData();
  } catch (error) {
    console.error('Error:', error);
  }
}

runLocalTest().catch(console.error);
