import { APIGatewayProxyEvent } from 'aws-lambda';
import { updateOrganizationLambda } from '../handler';
import { 
  createMockEvent, 
  createTestUser,
  expectErrorResponse,
  expectSuccessResponse
} from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { getTestUser } from '../../shared/testUtils/databaseTestUtils';
import { generateToken } from '../../shared/utils/tokenUtils';

describe('Update Organization Lambda Handler', () => {
  beforeAll(() => {
    process.env.JWT_SECRET = 'test-jwt-secret';
  });

  afterAll(() => {
    delete process.env.JWT_SECRET;
  });

  beforeEach(async () => {
    await clearTestData();
  });

  it.skip('should successfully update organization details for a parent account', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      organizationUpdateCount: 0,
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(testUser);

    const token = generateToken({
      userID: testUser.userID,
      email: testUser.email,
      accountType: testUser.accountType
    });

    const event = createMockEvent({
      organizationName: 'Test Organization',
      organizationUrl: 'https://test.com',
        authUrls: {
        signup: 'https://auth.test.com/signup',
        signin: 'https://auth.test.com/signin',
        verify: 'https://auth.test.com/verify',
        reset: 'https://auth.test.com/reset',
        update: 'https://auth.test.com/update',
        resend: 'https://auth.test.com/resend',
        successful: 'https://app.test.com/dashboard'
      }
    }, '/auth/update-organization');

    event.headers = { 'Authorization': `Bearer ${token}` };
    const response = await updateOrganizationLambda(event as APIGatewayProxyEvent);

    expectSuccessResponse(response, 200, {
      message: 'Organization details updated successfully',
      data: {
        organizationName: 'Test Organization',
        organizationUrl: 'https://test.com',
        authUrls: {
          signup: 'https://auth.test.com/signup',
          signin: 'https://auth.test.com/signin',
          verify: 'https://auth.test.com/verify',
          reset: 'https://auth.test.com/reset',
          update: 'https://auth.test.com/update',
          resend: 'https://auth.test.com/resend',
          successful: 'https://app.test.com/dashboard'
        }
      }
    });

    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser).toMatchObject({
      organizationName: 'Test Organization',
      organizationUrl: 'https://test.com',
      organizationUpdateCount: 1,
      authUrls: {
        signup: 'https://auth.test.com/signup',
        signin: 'https://auth.test.com/signin',
        verify: 'https://auth.test.com/verify',
        reset: 'https://auth.test.com/reset',
        update: 'https://auth.test.com/update',
        resend: 'https://auth.test.com/resend',
        successful: 'https://app.test.com/dashboard'
      }
    });
  });

  it.skip('should return error for child account trying to update organization details', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      organizationUpdateCount: 0,
      parentAccount: 'SOME_PARENT_KEY',
      accountType: 'child'
    });
    await seedTestUser(testUser);

    const token = generateToken({
      userID: testUser.userID,
      email: testUser.email,
      accountType: testUser.accountType
    });

    const event = createMockEvent({
      organizationName: 'Test Organization',
      organizationUrl: 'https://test.com'
    }, '/auth/update-organization');

    event.headers = { 'Authorization': `Bearer ${token}` };
    const response = await updateOrganizationLambda(event as APIGatewayProxyEvent);

    expectErrorResponse(response, 401, 'UNAUTHORIZED_ACCESS', 'Only parent accounts can update organization details');
  });

  it.skip('should return error for invalid organization name', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      organizationUpdateCount: 0,
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(testUser);

    const token = generateToken({
      userID: testUser.userID,
      email: testUser.email,
      accountType: testUser.accountType
    });

    // Test lowercase name
    let event = createMockEvent({
      organizationName: 'test organization',
      organizationUrl: 'https://test.com'
    }, '/auth/update-organization');
    event.headers = { 'Authorization': `Bearer ${token}` };
    let response = await updateOrganizationLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_ORGANIZATION_NAME', 
      'Organization name must start with a capital letter');

    // Test short name
    event = createMockEvent({
      organizationName: 'Tes',
      organizationUrl: 'https://test.com'
    }, '/auth/update-organization');
    event.headers = { 'Authorization': `Bearer ${token}` };
    response = await updateOrganizationLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_ORGANIZATION_NAME', 
      'Organization name must be at least 4 characters long');
  });

  it.skip('should return error for invalid organization URL', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      organizationUpdateCount: 0,
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(testUser);

    const token = generateToken({
      userID: testUser.userID,
      email: testUser.email,
      accountType: testUser.accountType
    });

    // Test invalid URL format
    let event = createMockEvent({
      organizationName: 'Test Organization',
      organizationUrl: 'invalid-url'
    }, '/auth/update-organization');
    event.headers = { 'Authorization': `Bearer ${token}` };
    let response = await updateOrganizationLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_ORGANIZATION_URL', 
      'Invalid URL format');

    // Test non-http(s) protocol
    event = createMockEvent({
      organizationName: 'Test Organization',
      organizationUrl: 'ftp://test.com'
    }, '/auth/update-organization');
    event.headers = { 'Authorization': `Bearer ${token}` };
    response = await updateOrganizationLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_ORGANIZATION_URL', 
      'Organization URL must start with http:// or https://');
  });

  it.skip('should return error when update limit reached', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      organizationUpdateCount: 1,
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(testUser);

    const token = generateToken({
      userID: testUser.userID,
      email: testUser.email,
      accountType: testUser.accountType
    });

    const event = createMockEvent({
      organizationName: 'Test Organization',
      organizationUrl: 'https://test.com'
    }, '/auth/update-organization');
    event.headers = { 'Authorization': `Bearer ${token}` };
    
    const response = await updateOrganizationLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 403, 'ORGANIZATION_UPDATE_LIMIT', 
      'Organization details can only be updated once');
  });

  it.skip('should return error for invalid auth URLs', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      organizationUpdateCount: 0,
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(testUser);

    const token = generateToken({
      userID: testUser.userID,
      email: testUser.email,
      accountType: testUser.accountType
    });

    const event = createMockEvent({
      organizationName: 'Test Organization',
      organizationUrl: 'https://test.com',
      authUrls: {
        signup: 'https://different-domain.com/signup',
        signin: 'https://test.com/signin',
        verify: 'https://test.com/verify',
        reset: 'https://test.com/reset',
        update: 'https://test.com/update',
        resend: 'https://test.com/resend',
        successful: 'https://test.com/dashboard'
      }
    }, '/auth/update-organization');

    event.headers = { 'Authorization': `Bearer ${token}` };
    const response = await updateOrganizationLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_AUTH_URLS', 
      'signupUrl must be under the organization\'s domain (test.com)');
  });
});
