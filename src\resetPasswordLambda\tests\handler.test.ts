import { APIGatewayProxyEvent, Context, Callback, APIGatewayProxyResult } from 'aws-lambda';
import { resetPasswordLambda } from '../handler';
import { generateOTP } from '../../shared/otp';
import { sendPasswordResetEmail } from '../../shared/emailService';
import { 
  createMockEvent, 
  createMockContext,
  createTestUser,
  expectErrorResponse,
  expectSuccessResponse
} from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, initializeTestDatabase, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { getTestUser } from '../../shared/testUtils/databaseTestUtils';
import { generatePublicKey } from '../../shared/utils/apiKeyUtils';

jest.mock('../../shared/emailService');
jest.mock('../../shared/otp');
jest.mock('../../shared/utils/tokenUtils');

describe('Reset Password Lambda Handler', () => {
  const mockContext = createMockContext();
  
  beforeAll(async () => {
    process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
    process.env.ENCRYPTION_KEY = "dBG9kF7bH5cJ2mN8pQ4sT6wX0yZ3vA1eI/nL+uM9oR4=";
    await initializeTestDatabase();
  });

  afterAll(async () => {
    delete process.env.USER_DETAILS_TABLE_NAME;
    delete process.env.ENCRYPTION_KEY;
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    (sendPasswordResetEmail as jest.Mock).mockResolvedValue(undefined);
    process.env.FRONTEND_URL = 'http://localhost:3000';
  });

  it.skip('should send reset password email successfully for parent account', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      accountType: 'parent',
      parentAccount: 'ROOT',
      resetPasswordOTP: '123456',
      resetPasswordOTPExpiry: Date.now() - 3600000,
      lastResetPasswordRequestAt: 0
    });
    
    await seedTestUser(testUser);
    (generateOTP as jest.Mock).mockReturnValue('654321');

    const event = createMockEvent({
      email: testUser.email
    }, '/reset-password');

    const response = await resetPasswordLambda(event as APIGatewayProxyEvent);

    expectSuccessResponse(response, 200, {
      message: 'Password reset link has been sent to your email'
    });

    expect(sendPasswordResetEmail).toHaveBeenCalledWith(
      testUser.email,
      expect.stringContaining('http://localhost:3000/reset-password?token=')
    );
    
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser).toMatchObject({
      resetPasswordOTP: '654321',
      resetPasswordOTPExpiry: expect.any(Number),
      lastResetPasswordRequestAt: expect.any(Number)
    });
  });

  it.skip('should send reset password email successfully for child account with valid parentPublicKey', async () => {
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent'
    });
    await seedTestUser(parentUser);

    const childUser = await createTestUser({
      emailVerified: true,
      accountType: 'child',
      parentAccount: parentUser.publicKey,
      resetPasswordOTP: '123456',
      resetPasswordOTPExpiry: Date.now() - 3600000,
      lastResetPasswordRequestAt: 0
    });
    await seedTestUser(childUser);
    (generateOTP as jest.Mock).mockReturnValue('654321');

    const event = createMockEvent({
      email: childUser.email,
      parentPublicKey: parentUser.publicKey
    }, '/reset-password');

    const response = await resetPasswordLambda(event as APIGatewayProxyEvent);

    expectSuccessResponse(response, 200, {
      message: 'Password reset link has been sent to your email'
    });

    expect(sendPasswordResetEmail).toHaveBeenCalledWith(
      childUser.email,
      expect.stringContaining('http://localhost:3000/reset-password?token=')
    );
    
    const updatedUser = await getTestUser(childUser.userID);
    expect(updatedUser).toMatchObject({
      resetPasswordOTP: '654321',
      resetPasswordOTPExpiry: expect.any(Number),
      lastResetPasswordRequestAt: expect.any(Number)
    });
  });

  it.skip('should return error for non-existent user', async () => {
    const event = createMockEvent({
      email: '<EMAIL>'
    }, '/reset-password');

    const response = await resetPasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 404, 'USER_NOT_FOUND', 'User not found');
  });

  it.skip('should return error for invalid email format', async () => {
    const event = createMockEvent({
      email: 'invalid-email'
    }, '/reset-password');

    const response = await resetPasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_EMAIL_FORMAT', 'Invalid email format');
  });

  it.skip('should return error for rate limit exceeded', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      lastResetPasswordRequestAt: Date.now() // Just sent OTP
    });
    
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email
    }, '/reset-password');

    const response = await resetPasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(
      response, 
      429, 
      'RATE_LIMIT_EXCEEDED', 
      'Please wait before requesting another reset link'
    );
  });

  it.skip('should return error for missing email', async () => {
    const event = createMockEvent({}, '/reset-password');

    const response = await resetPasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'MISSING_REQUIRED_FIELDS', 'Required fields are missing');
  });

  it.skip('should return error for inactive account', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      accountStatus: 'inactive'
    });
    
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email
    }, '/reset-password');

    const response = await resetPasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 403, 'ACCOUNT_INACTIVE', 'Password reset not allowed for inactive account');
  });

  it.skip('should return error for locked account', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      accountStatus: 'locked'
    });
    
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email
    }, '/reset-password');

    const response = await resetPasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 403, 'ACCOUNT_LOCKED', 'Password reset not allowed for locked account');
  });

  it.skip('should return error for unverified email', async () => {
    const testUser = await createTestUser({
      emailVerified: false
    });
    
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email
    }, '/reset-password');

    const response = await resetPasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 403, 'EMAIL_NOT_VERIFIED', 'Email must be verified before requesting password reset');
  });

  it.skip('should return error for invalid request body format', async () => {
    const event = createMockEvent({}, '/reset-password');
    event.body = 'invalid-json';

    const response = await resetPasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_REQUEST_BODY', 'Invalid JSON in request body');
  });

  it.skip('should create reset password fields in database', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      resetPasswordOTP: undefined,
      resetPasswordOTPExpiry: 0,
      lastResetPasswordRequestAt: 0
    });
    
    await seedTestUser(testUser);
    (generateOTP as jest.Mock).mockReturnValue('654321');

    const event = createMockEvent({
      email: testUser.email
    }, '/reset-password');

    await resetPasswordLambda(event as APIGatewayProxyEvent);
    
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser).toMatchObject({
      resetPasswordOTP: '654321',
      resetPasswordOTPExpiry: expect.any(Number),
      lastResetPasswordRequestAt: expect.any(Number)
    });
  });

  it.skip('should not affect email verification fields', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      verificationToken: '111111',
      verificationTokenExpiry: Date.now() + 3600000,
      resetPasswordOTP: undefined,
      resetPasswordOTPExpiry: 0,
      lastResetPasswordRequestAt: 0
    });
    
    await seedTestUser(testUser);
    (generateOTP as jest.Mock).mockReturnValue('654321');

    const event = createMockEvent({
      email: testUser.email
    }, '/reset-password');

    await resetPasswordLambda(event as APIGatewayProxyEvent);
    
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser).toMatchObject({
      verificationToken: '111111',
      verificationTokenExpiry: testUser.verificationTokenExpiry,
      resetPasswordOTP: '654321',
      resetPasswordOTPExpiry: expect.any(Number),
      lastResetPasswordRequestAt: expect.any(Number)
    });
  });

  it.skip('should return error if trying to reset child account password without parentApiKey', async () => {
    const parentPublicKey = generatePublicKey();
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent',
      publicKey: parentPublicKey,
      emailVerified: true,
      accountStatus: 'active'
    });
    await seedTestUser(parentUser);

    const childUser = await createTestUser({
      username: 'childuser',
      email: '<EMAIL>',
      parentAccount: parentPublicKey,
      accountType: 'child',
      emailVerified: true,
      accountStatus: 'active'
    });
    await seedTestUser(childUser);

    const event = createMockEvent({
      email: childUser.email
    }, '/reset-password');

    const response = await resetPasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'MISSING_PARENT_PUBLIC_KEY', 'Parent Public key is required for child accounts');
  });

  it.skip('should return error if child account provides incorrect parentPublicKey', async () => {
    const parentPublicKey = generatePublicKey();
    const wrongParentPublicKey = generatePublicKey();
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent',
      publicKey: parentPublicKey,
      emailVerified: true,
      accountStatus: 'active'
    });
    await seedTestUser(parentUser);

    const childUser = await createTestUser({
      username: 'childuser',
      email: '<EMAIL>',
      parentAccount: parentPublicKey,
      accountType: 'child',
      emailVerified: true,
      accountStatus: 'active'
    });
    await seedTestUser(childUser);

    const event = createMockEvent({
      email: childUser.email,
      parentPublicKey: wrongParentPublicKey
    }, '/reset-password');

    const response = await resetPasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 401, 'INVALID_PARENT_PUBLIC_KEY', 'Invalid parent Public key');
  });

  it.skip('should return error if parent account provides parentPublicKey', async () => {
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent',
      emailVerified: true,
      accountStatus: 'active'
    });
    await seedTestUser(parentUser);

    const event = createMockEvent({
      email: parentUser.email,
      parentPublicKey: generatePublicKey()
    }, '/reset-password');

    const response = await resetPasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_REQUEST', 'Parent Public key should not be provided for parent accounts');
  });

  it.skip('should send reset password email with parent organization name for child accounts', async () => {
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent',
      organizationName: 'Test Organization'
    });
    await seedTestUser(parentUser);

    const childUser = await createTestUser({
      emailVerified: true,
      accountType: 'child',
      parentAccount: parentUser.publicKey,
      resetPasswordOTP: '123456',
      resetPasswordOTPExpiry: Date.now() - 3600000,
      lastResetPasswordRequestAt: 0
    });
    await seedTestUser(childUser);
    (generateOTP as jest.Mock).mockReturnValue('654321');

    const event = createMockEvent({
      email: childUser.email,
      parentPublicKey: parentUser.publicKey
    }, '/reset-password');

    const response = await resetPasswordLambda(event as APIGatewayProxyEvent);

    expect(sendPasswordResetEmail).toHaveBeenCalledWith(
      childUser.email,
      expect.stringContaining('http://localhost:3000/reset-password?token='),
      'child',
      parentUser.publicKey
    );
  });
});

describe('Reset Password Counter Tests', () => {
  beforeAll(async () => {
    process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
    await initializeTestDatabase();
  });

  afterAll(async () => {
    delete process.env.USER_DETAILS_TABLE_NAME;
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    (sendPasswordResetEmail as jest.Mock).mockResolvedValue(undefined);
  });

  it.skip('should increment counter on successful reset request', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      accountType: 'parent',
      parentAccount: 'ROOT'
    });
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email
    }, '/reset-password');

    await resetPasswordLambda(event as APIGatewayProxyEvent);
    
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.resetPasswordRequestCount).toBe(1);
  });

  it.skip('should increment counter even when rate limited', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      lastResetPasswordRequestAt: Date.now() // Just sent
    });
    await seedTestUser(testUser);

    const event = createMockEvent({
      email: testUser.email
    }, '/reset-password');

    await resetPasswordLambda(event as APIGatewayProxyEvent);
    
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.resetPasswordRequestCount).toBe(1);
  });
}); 