import { SSM } from 'aws-sdk';

/**
 * Loads parameters from SSM Parameter Store based on environment
 */
export async function loadParametersFromEnv(): Promise<Record<string, any>> {
  const environment = process.env.ENVIRONMENT || 'live';
  const parameterName = environment === 'staging' ? 'staging-AUTHIQA_PARAMS' : 'AUTHIQA_PARAMS';
  
  console.log(`Loading parameters from SSM for ${environment} environment`);
  
  // Load from SSM
  const ssm = new SSM({ region: process.env.REGION || 'eu-west-1' });
  const result = await ssm.getParameter({ 
    Name: parameterName, 
    WithDecryption: true 
  }).promise();
  
  if (!result.Parameter?.Value) {
    throw new Error(`Parameter ${parameterName} not found or empty`);
  }
  
  // Parse JSON value
  return JSON.parse(result.Parameter.Value);
} 