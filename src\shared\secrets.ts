import { SecretsManager } from 'aws-sdk';
import { getSecret, getJsonSecret } from './services/configService';
import { APIGatewayProxyEvent } from 'aws-lambda';

// Cache for SMTP password
const secretCache = {
  password: '',
  expiry: 0
};

export const getSmtpPassword = async (event?: APIGatewayProxyEvent): Promise<string> => {
  // If we have a cached password and it's not expired, use it
  if (Date.now() < secretCache.expiry) {
    console.log('Using cached SMTP password');
    return secretCache.password;
  }

  // If we have an event, use the new config service
  if (event) {
    try {
      const secret = await getJsonSecret('SMTP_PASSWORD_SECRET', event);
      secretCache.password = secret.SMTP_PASSWORD;
      secretCache.expiry = Date.now() + 300000; // 5 minute cache
      return secretCache.password;
    } catch (error) {
      console.error('SMTP Secret Retrieval Failed:', error);
      throw new Error('Email service temporarily unavailable');
    }
  }

  // Fallback to the old method if no event is provided
  try {
    const sm = new SecretsManager({
      region: process.env.REGION || 'eu-west-1'
    });

    const data = await sm.getSecretValue({
      SecretId: process.env.SECRET_NAME!
    }).promise();

    if (!data.SecretString) {
      throw new Error('No secret string returned from Secrets Manager');
    }

    const secret = JSON.parse(data.SecretString);
    secretCache.password = secret.SMTP_PASSWORD;
    secretCache.expiry = Date.now() + 300000; // 5 minute cache

    return secretCache.password;
  } catch (error) {
    console.error('SMTP Secret Retrieval Failed:', error);
    throw new Error('Email service temporarily unavailable');
  }
};

// Local development fallback
if (process.env.NODE_ENV === 'local') {
  secretCache.password = process.env.LOCAL_SMTP_PASSWORD || '';
}

// Add new cache for Stripe secrets
const stripeSecretCache = {
  secretKey: '',
  publishableKey: '',
  webhookSecret: '',
  expiry: 0
};

export const getStripeSecrets = async (event?: APIGatewayProxyEvent): Promise<{
  secretKey: string;
  publishableKey: string;
  webhookSecret: string;
}> => {
  // If event is provided, determine environment
  let environment: 'staging' | 'live' = 'live';
  if (event) {
    const host = event.headers.Host || event.headers.host;
    environment = host?.includes('staging') ? 'staging' : 'live';
  }
  
  const secretName = environment === 'staging' ? 'STAGING_STRIPE_SECRETS' : 'STRIPE_SECRETS';

  if (Date.now() < stripeSecretCache.expiry) {
    console.log('Using cached Stripe secrets');
    return {
      secretKey: stripeSecretCache.secretKey,
      publishableKey: stripeSecretCache.publishableKey,
      webhookSecret: stripeSecretCache.webhookSecret
    };
  }

  // If we have an event, use the new config service
  if (event) {
    try {
      const secrets = await getJsonSecret(secretName, event);
      
      // Cache the secrets
      stripeSecretCache.secretKey = secrets.STRIPE_SECRET_KEY;
      stripeSecretCache.publishableKey = secrets.STRIPE_PUBLISHABLE_KEY;
      stripeSecretCache.webhookSecret = secrets.STRIPE_WEBHOOK_SECRET;
      stripeSecretCache.expiry = Date.now() + 300000; // 5 minute cache

      return {
        secretKey: stripeSecretCache.secretKey,
        publishableKey: stripeSecretCache.publishableKey,
        webhookSecret: stripeSecretCache.webhookSecret
      };
    } catch (error) {
      console.error('Stripe Secret Retrieval Failed:', error);
      throw new Error('Payment service temporarily unavailable');
    }
  }

  // Fallback to the old method if no event is provided
  try {
    const sm = new SecretsManager({
      region: process.env.REGION || 'eu-west-1'
    });
    
    const data = await sm.getSecretValue({
      SecretId: process.env.STRIPE_SECRETS_NAME!
    }).promise();

    if (!data.SecretString) {
      throw new Error('No secret string returned from Secrets Manager');
    }

    const secrets = JSON.parse(data.SecretString);
    
    // Cache the secrets
    stripeSecretCache.secretKey = secrets.STRIPE_SECRET_KEY;
    stripeSecretCache.publishableKey = secrets.STRIPE_PUBLISHABLE_KEY;
    stripeSecretCache.webhookSecret = secrets.STRIPE_WEBHOOK_SECRET;
    stripeSecretCache.expiry = Date.now() + 300000; // 5 minute cache like SMTP

    return {
      secretKey: stripeSecretCache.secretKey,
      publishableKey: stripeSecretCache.publishableKey,
      webhookSecret: stripeSecretCache.webhookSecret
    };
  } catch (error) {
    console.error('Stripe Secret Retrieval Failed:', error);
    throw new Error('Payment service temporarily unavailable');
  }
};

// Add local development fallback for Stripe
if (process.env.NODE_ENV === 'local') {
  stripeSecretCache.secretKey = process.env.LOCAL_STRIPE_SECRET_KEY || '';
  stripeSecretCache.publishableKey = process.env.LOCAL_STRIPE_PUBLISHABLE_KEY || '';
  stripeSecretCache.webhookSecret = process.env.LOCAL_STRIPE_WEBHOOK_SECRET || '';
}
