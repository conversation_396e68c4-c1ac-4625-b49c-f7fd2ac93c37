import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { verifyToken } from '../shared/utils/tokenUtils';
import { getChildAccountsByParent, getParentAccountOperations, getUserByPublicKey, updateUserLastBalanceNotification } from '../shared/database/userOperations';
import { CostBreakdown, OperationKey, ParentAccountBalance, User } from '../shared/types';
import { OPERATION_COSTS, COST_CALCULATOR_OPERATIONS, AUTHIQA_MARGIN, PRICES, STORAGE_COST_PER_KB } from '../shared/constants';
import { getParentAccountBalances, updateParentAccountBalances } from '../shared/database/balanceOperations';
import { sendLowBalanceEmail, LowBalanceEmailData } from '../shared/emailService';
import { getConfig } from '../shared/services/configService';

// Parent-only operations that shouldn't include child counts
const PARENT_ONLY_OPERATIONS = [
  'organizationUpdate',
  'organizationDetailsRetrieval',
  'childAccountsListRetrieval'
];

// Function to handle balance notifications
async function handleLowBalanceNotifications(
  user: User,
  balances: ParentAccountBalance,
  usagePercentage: number,
  availableBalance: number,
  config: any,
  event?: APIGatewayProxyEvent
): Promise<void> {
  const now = Date.now();
  const ONE_DAY = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  
  // Determine which notification to send based on usage percentage
  if (usagePercentage >= 100 || availableBalance <= 0) {
    // Depleted notification (100% used)
    const lastNotificationTime = user.lastDepletedBalanceNotificationAt || 0;
    if (now - lastNotificationTime > ONE_DAY) {
      try {
        const emailData: LowBalanceEmailData = {
          username: user.username,
          accountBalance: balances.accountBalance,
          availableBalance,
          usagePercentage,
          alertType: 'depleted'
        };
        
        // Pass the event parameter to use the correct table name
        await sendLowBalanceEmail(user.email, emailData, 'parent', event);
        await updateUserLastBalanceNotification(user.userID, 'depleted', now, config.USER_DETAILS_TABLE_NAME);
        console.log('Depleted balance notification sent to:', user.email);
      } catch (error) {
        console.error('Failed to send depleted balance notification:', error);
      }
    }
  } else if (usagePercentage >= 97) {
    // Critical notification (97% used)
    const lastNotificationTime = user.lastCriticalBalanceNotificationAt || 0;
    if (now - lastNotificationTime > ONE_DAY) {
      try {
        const emailData: LowBalanceEmailData = {
          username: user.username,
          accountBalance: balances.accountBalance,
          availableBalance,
          usagePercentage,
          alertType: 'critical'
        };
        
        // Pass the event parameter to use the correct table name
        await sendLowBalanceEmail(user.email, emailData, 'parent', event);
        await updateUserLastBalanceNotification(user.userID, 'critical', now, config.USER_DETAILS_TABLE_NAME);
        console.log('Critical balance notification sent to:', user.email);
      } catch (error) {
        console.error('Failed to send critical balance notification:', error);
      }
    }
  } else if (usagePercentage >= 80) {
    // Warning notification (80% used)
    const lastNotificationTime = user.lastLowBalanceNotificationAt || 0;
    if (now - lastNotificationTime > ONE_DAY) {
      try {
        const emailData: LowBalanceEmailData = {
          username: user.username,
          accountBalance: balances.accountBalance,
          availableBalance,
          usagePercentage,
          alertType: 'warning'
        };
        
        // Pass the event parameter to use the correct table name
        await sendLowBalanceEmail(user.email, emailData, 'parent', event);
        await updateUserLastBalanceNotification(user.userID, 'warning', now, config.USER_DETAILS_TABLE_NAME);
        console.log('Low balance notification sent to:', user.email);
      } catch (error) {
        console.error('Failed to send low balance notification:', error);
      }
    }
  }
}

// Extract the cost calculation logic to avoid duplication
async function calculateCosts(publicKey: string, config: any, event?: APIGatewayProxyEvent) {
  // Get operation counts with table name from config
  const parentOps = await getParentAccountOperations(publicKey, config.USER_DETAILS_TABLE_NAME);
  const childOps = await getChildAccountsByParent(publicKey, undefined, undefined, undefined, undefined, undefined, config.USER_DETAILS_TABLE_NAME);
  
  // Calculate total counts
  const totalCounts = {
    emailConfirmation: parentOps.emailConfirmation + childOps.operationCounts.emailConfirmation,
    resendConfirmation: parentOps.resendConfirmation + childOps.operationCounts.resendConfirmation,
    resetPassword: parentOps.resetPassword + childOps.operationCounts.resetPassword,
    updatePassword: parentOps.updatePassword + childOps.operationCounts.updatePassword,
    signIn: parentOps.signIn + childOps.operationCounts.signIn,
    organizationUpdate: parentOps.organizationUpdate, // Parent only
    organizationDetailsRetrieval: parentOps.organizationDetailsRetrieval, // Parent only
    childAccountsListRetrieval: parentOps.childAccountsListRetrieval, // Parent only
    childAccounts: childOps.totalAccounts // Total number of child accounts
  };

  // Calculate costs
  const costs: CostBreakdown = {
    reads: {} as { [key in OperationKey]: number },
    writes: {} as { [key in OperationKey]: number },
    totalCost: 0
  };

  // Process each operation
  Object.entries(OPERATION_COSTS).forEach(([operation, operationCost]) => {
    const operationKey = operation as OperationKey;
    
    // Get parent count, default to 0 if undefined/null/NaN
    const parentCount = Number(parentOps[operationKey]) || 0;
    
    // Get child count, 0 for parent-only operations
    const childCount = PARENT_ONLY_OPERATIONS.includes(operationKey) 
      ? 0 
      : (Number(childOps.operationCounts[operationKey]) || 0);

    // Total operations for this endpoint
    let totalCount = parentCount + childCount;

    // Special handling for childAccounts operation
    if (operationKey === 'childAccounts') {
      totalCount = childOps.totalAccounts; // Don't add 1, as parent isn't a child
    }

    // Calculate read and write costs
    const readCost = Number((totalCount * operationCost.reads * PRICES.read / 1000000).toFixed(2));
    const writeCost = Number((totalCount * operationCost.writes * PRICES.write / 1000000).toFixed(2));

    // Store in breakdown
    costs.reads[operationKey] = readCost;
    costs.writes[operationKey] = writeCost;
    costs.totalCost += readCost + writeCost;
  });

  // Add cost calculator operations
  const costCalculatorCost = Number((COST_CALCULATOR_OPERATIONS.reads * PRICES.read * COST_CALCULATOR_OPERATIONS.expectedCalls / 1000000).toFixed(2));

  // Add parent signup cost
  const parentSignupCost = 0.*********;  // Cost for parent account creation
  
  // Calculate storage cost
  const baseStorageCost = Number((childOps.totalAccounts * STORAGE_COST_PER_KB).toFixed(2));
  const totalStorageCost = Number((baseStorageCost * (1 + AUTHIQA_MARGIN)).toFixed(2));
  
  // Calculate final costs
  const baseCost = Number((costs.totalCost + costCalculatorCost + parentSignupCost + baseStorageCost).toFixed(2));
  const marginCost = Number((baseCost * AUTHIQA_MARGIN).toFixed(2));
  const totalCostWithMargin = Number((baseCost + marginCost).toFixed(2));

  // Calculate total operations count
  const totalOperationsCount = Object.values(totalCounts).reduce((sum, count) => sum + count, 0);

  // Calculate child accounts cost
  const childAccountsCost = Number(costs.reads['childAccounts'] + costs.writes['childAccounts']).toFixed(2);

  // Calculate IO associated cost
  const IOassociatedCost = Number(
    Object.entries(OPERATION_COSTS).reduce((total, [operation, _]) => {
      const operationKey = operation as OperationKey;
      if (operationKey !== 'childAccounts') { // Exclude child accounts cost
        total += costs.reads[operationKey] + costs.writes[operationKey];
      }
      return total;
    }, 0)
  ).toFixed(2);

  // Get user and balances with table name from config
  const user = await getUserByPublicKey(publicKey, config.USER_DETAILS_TABLE_NAME);
  if (!user) {
    throw new Error('User not found');
  }

  // Get balances with table name from config
  // FIXED: Use USER_DETAILS_TABLE_NAME instead of BILLING_TABLE_NAME since balances are stored in the user table
  const balances = await getParentAccountBalances(user.userID, config.USER_DETAILS_TABLE_NAME);

  // Calculate available balance
  const availableBalance = Number((balances.availableBalance - totalCostWithMargin).toFixed(2));

  // Calculate usage percentage
  const usagePercentage = balances.accountBalance > 0 
    ? Number(((totalCostWithMargin / balances.accountBalance) * 100).toFixed(2)) 
    : 100;

  // Determine low balance alert
  const lowBalanceAlert = usagePercentage >= 80;

  // Update available balance in database with table name from config
  // FIXED: Use USER_DETAILS_TABLE_NAME for updating balances as well
  const currentBalance = user.accountBalance || 0;
  const balanceChange = balances.accountBalance - currentBalance;
  await updateParentAccountBalances(user.userID, balanceChange, config.USER_DETAILS_TABLE_NAME);

  // Handle low balance notifications if needed
  if (user.accountType === 'parent') {
    await handleLowBalanceNotifications(user, balances, usagePercentage, availableBalance, config, event);
  }

  return {
    user,
    operationCounts: {
      parent: parentOps,
      children: childOps.operationCounts,
      totalAccounts: childOps.totalAccounts,
      totalOperationsCount
    },
    costs: {
      baseCost: baseCost.toFixed(2),
      totalCost: totalCostWithMargin.toFixed(2),
      breakdown: {
        ...Object.entries(OPERATION_COSTS).reduce((acc, [operation, _]) => {
          const operationKey = operation as OperationKey;
          acc[operationKey] = Number(costs.reads[operationKey] + costs.writes[operationKey]).toFixed(2);
          return acc;
        }, {} as Record<OperationKey, string>),
        costCalculator: costCalculatorCost.toFixed(2),
        IOassociatedCost,
        childAccounts: childAccountsCost,
        storage: totalStorageCost.toFixed(2)
      }
    },
    balance: {
      accountBalance: balances.accountBalance,
      availableBalance,
      currentCharges: totalCostWithMargin,
      usagePercentage: totalCostWithMargin > 0 
        ? Math.round((totalCostWithMargin / balances.availableBalance) * 100) 
        : 0,
      lowBalanceAlert
    }
  };
}

export const costCalculatorLambda = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  // Load configuration from the service
  const config = await getConfig(event);
  
  console.log('[COST-CALCULATOR] Lambda invoked with headers:', {
    host: event.headers.Host || event.headers.host,
    origin: event.headers.Origin || event.headers.origin
  });
  
  console.log('[CONFIG] CostCalculatorLambda using configuration service', {
    environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
    userTable: config.USER_DETAILS_TABLE_NAME
  });

  try {
    // Check for internal service call
    const internalServiceKey = event.headers['x-internal-service'];
    if (internalServiceKey === process.env.INTERNAL_SERVICE_KEY) {
      console.log('Internal service call detected');
      const publicKey = event.headers['x-public-key'];
      
      if (!publicKey) {
        console.log('Internal service call missing public key');
        return { statusCode: 401, body: JSON.stringify({ error: 'Missing public key' }) };
      }

      const user = await getUserByPublicKey(publicKey, config.USER_DETAILS_TABLE_NAME);
      if (!user || user.accountType !== 'parent') {
        console.log('Invalid public key or non-parent account');
        return { statusCode: 401, body: JSON.stringify({ error: 'Invalid public key' }) };
      }

      // Skip JWT validation and go straight to cost calculation
      try {
        const result = await calculateCosts(publicKey, config, event);
        
        return {
          statusCode: 200,
          body: JSON.stringify({
            operationCounts: result.operationCounts,
            costs: result.costs,
            balance: result.balance,
            currency: 'USD'
          })
        };
      } catch (error) {
        console.error('Cost calculation error:', error);
        return { 
          statusCode: 500, 
          body: JSON.stringify({ error: 'Failed to calculate costs' })
        };
      }
    }

    // Regular JWT token validation
    const authHeader = event.headers.Authorization || event.headers.authorization;
    console.log('Auth Header Found:', {
      exists: !!authHeader,
      headerValue: authHeader ? `${authHeader.substring(0, 20)}...` : 'none'
    });
    
    if (!authHeader) {
      console.log('Authorization Failed: No auth header');
      return { statusCode: 401, body: JSON.stringify({ error: 'Unauthorized' }) };
    }

    const token = authHeader.replace('Bearer ', '');
    console.log('Token Processing:', {
      tokenLength: token.length,
      firstChars: token.substring(0, 20),
      lastChars: token.substring(token.length - 20)
    });
    
    try {
      const decodedToken = await verifyToken(token);
      
      if (!decodedToken) {
        console.log('Token Verification Failed: Token could not be decoded');
        return { statusCode: 401, body: JSON.stringify({ error: 'Invalid token' }) };
      }

      if (decodedToken.accountType !== 'parent') {
        console.log('Authorization Failed: Invalid account type', {
          expected: 'parent',
          received: decodedToken.accountType
        });
        return { statusCode: 401, body: JSON.stringify({ error: 'Only parent accounts can access cost analysis' }) };
      }

      if (!decodedToken.publicKey) {
        console.log('Authorization Failed: No public key in token');
        return { statusCode: 401, body: JSON.stringify({ error: 'Invalid token structure' }) };
      }

      try {
        const result = await calculateCosts(decodedToken.publicKey, config, event);
        
        return {
          statusCode: 200,
          body: JSON.stringify({
            operationCounts: result.operationCounts,
            costs: result.costs,
            balance: result.balance,
            currency: 'USD'
          })
        };
      } catch (error) {
        console.error('Database operation error:', error);
        return { 
          statusCode: 500, 
          body: JSON.stringify({ error: 'Failed to retrieve operations' })
        };
      }
    } catch (error) {
      console.error('Token verification error:', error);
      return { 
        statusCode: 500, 
        body: JSON.stringify({ error: 'Failed to verify token' })
      };
    }
  } catch (error) {
    console.error('Cost calculation error:', error);
    return { 
      statusCode: 500, 
      body: JSON.stringify({ error: 'Failed to calculate costs' })
    };
  }
}; 
 

