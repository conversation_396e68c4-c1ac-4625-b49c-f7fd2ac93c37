import { APIGatewayProxyEvent } from 'aws-lambda';
import { getChildAccountsLambda } from '../handler';
import { 
    createMockEvent, 
    createTestUser,
    expectErrorResponse,
    expectSuccessResponse,
    VALID_TEST_PASSWORD
} from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { verifyToken } from '../../shared/utils/tokenUtils';
import { generatePublicKey } from '../../shared/utils/apiKeyUtils';
import { getTestUser } from '../../shared/testUtils/databaseTestUtils';

jest.mock('../../shared/utils/tokenUtils');

describe('GetChildAccounts Lambda Handler', () => {
    beforeAll(() => {
        process.env.ENCRYPTION_KEY = "dBG9kF7bH5cJ2mN8pQ4sT6wX0yZ3vA1eI/nL+uM9oR4=";
    });

    afterAll(() => {
        delete process.env.ENCRYPTION_KEY;
    });

    beforeEach(async () => {
        await clearTestData();
        jest.clearAllMocks();
        (verifyToken as jest.Mock).mockReturnValue({
            userID: 'test-user-id',
            email: '<EMAIL>',
            accountType: 'parent',
            publicKey: 'test-public-key'
        });
    });

    const createEventWithAuth = (path: string, body = {}, queryParams = {}) => {
        const event = createMockEvent(body, path);
        event.headers = {
            ...event.headers,
            Authorization: 'Bearer valid-token'
        };
        if (Object.keys(queryParams).length > 0) {
            event.queryStringParameters = queryParams;
        }
        return event;
    };

    it.skip('should list child accounts successfully for parent account', async () => {
        // Create parent user
        const parentUser = await createTestUser({
            username: 'parentuser',
            email: '<EMAIL>',
            accountType: 'parent',
            parentAccount: 'ROOT'
        });
        await seedTestUser(parentUser);

        // Create some child accounts
        const childUser1 = await createTestUser({
            username: 'child1',
            email: '<EMAIL>',
            accountType: 'child',
            parentAccount: parentUser.publicKey,
            lastLoginAttempt: Date.now() - (15 * 24 * 60 * 60 * 1000), // 15 days ago
            createdAt: Date.now() - (60 * 24 * 60 * 60 * 1000) // 60 days ago
        });
        await seedTestUser(childUser1);

        const childUser2 = await createTestUser({
            username: 'child2',
            email: '<EMAIL>',
            accountType: 'child',
            parentAccount: parentUser.publicKey,
            lastLoginAttempt: Date.now() - (40 * 24 * 60 * 60 * 1000), // 40 days ago
            createdAt: Date.now() - (90 * 24 * 60 * 60 * 1000) // 90 days ago
        });
        await seedTestUser(childUser2);

        (verifyToken as jest.Mock).mockReturnValue({
            userID: parentUser.userID,
            email: parentUser.email,
            accountType: 'parent',
            publicKey: parentUser.publicKey
        });

        const event = createEventWithAuth('/auth/child-accounts');
        const response = await getChildAccountsLambda(event as APIGatewayProxyEvent);
        
        expectSuccessResponse(response, 200, {
            data: {
                stats: {
                    total: 2,
                    active: 1 // Only child1 is active in last 30 days
                },
                accounts: expect.arrayContaining([
                    expect.objectContaining({
                        username: 'child1',
                        email: '<EMAIL>',
                        lastSeen: expect.any(String),
                        registered: expect.any(String)
                    }),
                    expect.objectContaining({
                        username: 'child2',
                        email: '<EMAIL>',
                        lastSeen: expect.any(String),
                        registered: expect.any(String)
                    })
                ]),
                pagination: {
                    limit: 50,
                    hasMore: false
                }
            }
        });
    });

    it.skip('should filter accounts by search term', async () => {
        const parentUser = await createTestUser({
            username: 'parentuser',
            email: '<EMAIL>',
            accountType: 'parent',
            parentAccount: 'ROOT'
        });
        await seedTestUser(parentUser);

        // Create child accounts with specific patterns
        const childUser1 = await createTestUser({
            username: 'searchable',
            email: '<EMAIL>',
            accountType: 'child',
            parentAccount: parentUser.publicKey
        });
        await seedTestUser(childUser1);

        const childUser2 = await createTestUser({
            username: 'other',
            email: '<EMAIL>',
            accountType: 'child',
            parentAccount: parentUser.publicKey
        });
        await seedTestUser(childUser2);

        (verifyToken as jest.Mock).mockReturnValue({
            userID: parentUser.userID,
            email: parentUser.email,
            accountType: 'parent',
            publicKey: parentUser.publicKey
        });

        const event = createEventWithAuth('/auth/child-accounts', {}, { search: 'searchable' });
        const response = await getChildAccountsLambda(event as APIGatewayProxyEvent);
        
        expectSuccessResponse(response, 200, {
            data: {
                stats: {
                    total: 2,
                    active: 0
                },
                accounts: expect.arrayContaining([
                    expect.objectContaining({
                        username: 'searchable',
                        email: '<EMAIL>'
                    }),
                    expect.objectContaining({
                        username: 'other',
                        email: '<EMAIL>'
                    })
                ]),
                pagination: expect.any(Object)
            }
        });
    });

    it.skip('should filter accounts by date range', async () => {
        const parentUser = await createTestUser({
            username: 'parentuser',
            email: '<EMAIL>',
            accountType: 'parent',
            parentAccount: 'ROOT'
        });
        await seedTestUser(parentUser);

        const now = Date.now();
        const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);

        // Create child accounts with different creation dates
        const childUser1 = await createTestUser({
            username: 'recent',
            email: '<EMAIL>',
            accountType: 'child',
            parentAccount: parentUser.publicKey,
            createdAt: now - (15 * 24 * 60 * 60 * 1000) // 15 days ago
        });
        await seedTestUser(childUser1);

        const childUser2 = await createTestUser({
            username: 'old',
            email: '<EMAIL>',
            accountType: 'child',
            parentAccount: parentUser.publicKey,
            createdAt: now - (45 * 24 * 60 * 60 * 1000) // 45 days ago
        });
        await seedTestUser(childUser2);

        (verifyToken as jest.Mock).mockReturnValue({
            userID: parentUser.userID,
            email: parentUser.email,
            accountType: 'parent',
            publicKey: parentUser.publicKey
        });

        const event = createEventWithAuth('/auth/child-accounts', {}, {
            startDate: thirtyDaysAgo.toString(),
            endDate: now.toString()
        });

        console.log('Date Range Test - Query Parameters:', {
            startDate: new Date(thirtyDaysAgo).toISOString(),
            endDate: new Date(now).toISOString()
        });

        const response = await getChildAccountsLambda(event as APIGatewayProxyEvent);
        console.log('Date Range Test - Raw Response:', {
            statusCode: response.statusCode,
            parsedBody: JSON.parse(response.body)
        });
        
        expectSuccessResponse(response, 200, {
            data: {
                stats: {
                    total: 1,
                    active: 0
                },
                accounts: expect.arrayContaining([
                    expect.objectContaining({
                        username: 'recent',
                        email: '<EMAIL>'
                    })
                ]),
                pagination: expect.any(Object)
            }
        });
        
        // Ensure old account is not in the results
        const body = JSON.parse(response.body);
        expect(body.data.data.accounts).not.toContainEqual(
            expect.objectContaining({
                username: 'old',
                email: '<EMAIL>'
            })
        );
    });

    it.skip('should filter accounts using human readable date format (YYYY-MM-DD)', async () => {
        const parentUser = await createTestUser({
            username: 'parentuser',
            email: '<EMAIL>',
            accountType: 'parent',
            parentAccount: 'ROOT'
        });
        await seedTestUser(parentUser);

        // Create child accounts with different creation dates
        const childUser1 = await createTestUser({
            username: 'recent',
            email: '<EMAIL>',
            accountType: 'child',
            parentAccount: parentUser.publicKey,
            createdAt: new Date('2024-01-15').getTime() // January 15, 2024
        });
        await seedTestUser(childUser1);

        const childUser2 = await createTestUser({
            username: 'old',
            email: '<EMAIL>',
            accountType: 'child',
            parentAccount: parentUser.publicKey,
            createdAt: new Date('2023-12-15').getTime() // December 15, 2023
        });
        await seedTestUser(childUser2);

        (verifyToken as jest.Mock).mockReturnValue({
            userID: parentUser.userID,
            email: parentUser.email,
            accountType: 'parent',
            publicKey: parentUser.publicKey
        });

        // Using YYYY-MM-DD format
        const event = createEventWithAuth('/auth/child-accounts', {}, {
            startDate: '2024-01-01',  // January 1, 2024
            endDate: '2024-01-31'     // January 31, 2024
        });

        const response = await getChildAccountsLambda(event as APIGatewayProxyEvent);
        
        expectSuccessResponse(response, 200, {
            data: {
                stats: {
                    total: 1,
                    active: 0
                },
                accounts: expect.arrayContaining([
                    expect.objectContaining({
                        username: 'recent',
                        email: '<EMAIL>'
                    })
                ]),
                pagination: expect.any(Object)
            }
        });
        
        // Ensure old account is not in the results
        const body = JSON.parse(response.body);
        expect(body.data.data.accounts).not.toContainEqual(
            expect.objectContaining({
                username: 'old',
                email: '<EMAIL>'
            })
        );
    });

    it.skip('should filter accounts using ISO 8601 date format', async () => {
        const parentUser = await createTestUser({
            username: 'parentuser',
            email: '<EMAIL>',
            accountType: 'parent',
            parentAccount: 'ROOT'
        });
        await seedTestUser(parentUser);

        // Create child accounts with different creation dates
        const childUser1 = await createTestUser({
            username: 'recent',
            email: '<EMAIL>',
            accountType: 'child',
            parentAccount: parentUser.publicKey,
            createdAt: new Date('2024-01-15T12:00:00Z').getTime()
        });
        await seedTestUser(childUser1);

        const childUser2 = await createTestUser({
            username: 'old',
            email: '<EMAIL>',
            accountType: 'child',
            parentAccount: parentUser.publicKey,
            createdAt: new Date('2023-12-15T12:00:00Z').getTime()
        });
        await seedTestUser(childUser2);

        (verifyToken as jest.Mock).mockReturnValue({
            userID: parentUser.userID,
            email: parentUser.email,
            accountType: 'parent',
            publicKey: parentUser.publicKey
        });

        // Using ISO 8601 format
        const event = createEventWithAuth('/auth/child-accounts', {}, {
            startDate: '2024-01-01T00:00:00Z',
            endDate: '2024-01-31T23:59:59Z'
        });

        const response = await getChildAccountsLambda(event as APIGatewayProxyEvent);
        
        expectSuccessResponse(response, 200, {
            data: {
                stats: {
                    total: 1,
                    active: 0
                },
                accounts: expect.arrayContaining([
                    expect.objectContaining({
                        username: 'recent',
                        email: '<EMAIL>'
                    })
                ]),
                pagination: expect.any(Object)
            }
        });
        
        // Ensure old account is not in the results
        const body = JSON.parse(response.body);
        expect(body.data.data.accounts).not.toContainEqual(
            expect.objectContaining({
                username: 'old',
                email: '<EMAIL>'
            })
        );
    });

    it.skip('should return error for invalid date string format', async () => {
        const event = createEventWithAuth('/auth/child-accounts', {}, {
            startDate: 'not-a-date',
            endDate: '2024-01-31'
        });

        const response = await getChildAccountsLambda(event as APIGatewayProxyEvent);
        expectErrorResponse(response, 400, 'INVALID_DATE_FORMAT', 
            'Start date must be either a timestamp or a valid date format (YYYY-MM-DD or ISO 8601)');
    });

    it.skip('should handle pagination correctly', async () => {
        const parentUser = await createTestUser({
            username: 'parentuser',
            email: '<EMAIL>',
            accountType: 'parent',
            parentAccount: 'ROOT'
        });
        await seedTestUser(parentUser);

        // Create multiple child accounts
        for (let i = 0; i < 55; i++) {
            const childUser = await createTestUser({
                username: `child${i}`,
                email: `child${i}@example.com`,
                accountType: 'child',
                parentAccount: parentUser.publicKey
            });
            await seedTestUser(childUser);
        }

        (verifyToken as jest.Mock).mockReturnValue({
            userID: parentUser.userID,
            email: parentUser.email,
            accountType: 'parent',
            publicKey: parentUser.publicKey
        });

        // First page
        const firstEvent = createEventWithAuth('/auth/child-accounts', {}, { limit: '50' });
        const firstResponse = await getChildAccountsLambda(firstEvent as APIGatewayProxyEvent);
        console.log('Pagination Test - First Page Response:', {
            statusCode: firstResponse.statusCode,
            parsedBody: JSON.parse(firstResponse.body)
        });

        const firstBody = JSON.parse(firstResponse.body);
        
        expect(firstBody.data.data.accounts.length).toBe(50);
        expect(firstBody.data.data.pagination.hasMore).toBe(true);
        expect(firstBody.data.data.pagination.nextPageToken).toBeDefined();

        // Second page
        const secondEvent = createEventWithAuth('/auth/child-accounts', {}, {
            limit: '50',
            lastKey: firstBody.data.data.pagination.nextPageToken
        });

        const secondResponse = await getChildAccountsLambda(secondEvent as APIGatewayProxyEvent);
        console.log('Pagination Test - Second Page Response:', {
            statusCode: secondResponse.statusCode,
            parsedBody: JSON.parse(secondResponse.body)
        });

        const secondBody = JSON.parse(secondResponse.body);

         expect(secondBody.data.data.accounts.length).toBe(5);
        expect(secondBody.data.data.pagination.hasMore).toBe(false);
        expect(secondBody.data.data.pagination.nextPageToken).toBeUndefined();
    });

    it.skip('should return error for non-parent account', async () => {
        (verifyToken as jest.Mock).mockReturnValue({
            userID: 'test-user-id',
            email: '<EMAIL>',
            accountType: 'child',
            publicKey: 'test-public-key'
        });

        const event = createEventWithAuth('/auth/child-accounts');
        const response = await getChildAccountsLambda(event as APIGatewayProxyEvent);
        expectErrorResponse(response, 401, 'UNAUTHORIZED', 'Only parent accounts can access this data');
    });

    it.skip('should return error for missing authorization', async () => {
        const event = createMockEvent({}, '/auth/child-accounts');
        event.headers = {}; // Empty headers to test missing authorization
        
        const response = await getChildAccountsLambda(event as APIGatewayProxyEvent);
        expectErrorResponse(response, 401, 'UNAUTHORIZED', 'Missing authorization token');
    });

    it.skip('should return error for invalid date format', async () => {
        const event = createEventWithAuth('/auth/child-accounts', {}, {
            startDate: 'invalid-date',
            endDate: Date.now().toString()
        });

        const response = await getChildAccountsLambda(event as APIGatewayProxyEvent);
        expectErrorResponse(response, 400, 'INVALID_DATE_FORMAT', 'Start date must be a valid timestamp');
    });

    it.skip('should return error for invalid limit', async () => {
        const event = createEventWithAuth('/auth/child-accounts', {}, { limit: '101' });

        const response = await getChildAccountsLambda(event as APIGatewayProxyEvent);
        expectErrorResponse(response, 400, 'INVALID_LIMIT', 'Limit must be between 1 and 100');
    });

    it.skip('should increment child accounts list retrieval count', async () => {
        const parentUser = await createTestUser({
            username: 'parentuser',
            email: '<EMAIL>',
            accountType: 'parent',
            parentAccount: 'ROOT',
            childAccountsListRetrievalCount: 0
        });
        await seedTestUser(parentUser);

        (verifyToken as jest.Mock).mockReturnValue({
            userID: parentUser.userID,
            email: parentUser.email,
            accountType: 'parent',
            publicKey: parentUser.publicKey
        });

        const event = createEventWithAuth('/auth/child-accounts');
        const response = await getChildAccountsLambda(event as APIGatewayProxyEvent);
        
        const updatedUser = await getTestUser(parentUser.userID);
        expect(updatedUser?.childAccountsListRetrievalCount).toBe(1);
    });

    it.skip('should handle database errors when incrementing retrieval count', async () => {
        const parentUser = await createTestUser({
            accountType: 'parent',
            parentAccount: 'ROOT'
        });
        await seedTestUser(parentUser);

        (verifyToken as jest.Mock).mockReturnValue({
            userID: parentUser.userID,
            email: parentUser.email,
            accountType: 'parent',
            publicKey: parentUser.publicKey
        });

        // Mock database error for increment
        jest.spyOn(require('../../shared/database/userOperations'), 'incrementChildAccountsListRetrievalCount')
            .mockRejectedValueOnce(new Error('Database error'));

        const event = createEventWithAuth('/auth/child-accounts');
        const response = await getChildAccountsLambda(event as APIGatewayProxyEvent);
        expect(response.statusCode).toBe(200);
    });
}); 