import { DynamoDB } from 'aws-sdk';
import { ParentAccountBalance } from '../types';

const dynamoDB = new DynamoDB.DocumentClient();
const TABLE_NAME = process.env.BILLING_TABLE_NAME || 'authiqaBilling';

export const updateParentAccountBalances = async (
  userID: string,
  balanceChange: number,
  tableName?: string
): Promise<void> => {
  try {
    console.log(`Starting balance update for user ${userID}, change: ${balanceChange}`);
    
    const params = {
      TableName: tableName || TABLE_NAME,
      Key: { userID },
      UpdateExpression: 'SET accountBalance = accountBalance + :change, availableBalance = availableBalance + :change',
      ConditionExpression: 'accountType = :parentType AND attribute_exists(accountBalance)',
      ExpressionAttributeValues: {
        ':change': Number(balanceChange.toFixed(2)),
        ':parentType': 'parent'
      }
    };
    
    await dynamoDB.update(params).promise();
    console.log(`Successfully updated balances for user ${userID}, change: ${balanceChange}`);
  } catch (error) {
    console.error(`Error updating balances for user ${userID}:`, error);
    throw error;
  }
};

export const getParentAccountBalances = async (
  userID: string,
  tableName?: string
): Promise<ParentAccountBalance> => {
  try {
    console.log(`Getting balances for user ${userID}`);
    
    const params = {
      TableName: tableName || TABLE_NAME,
      Key: { userID }
    };
    
    const result = await dynamoDB.get(params).promise();
    console.log(`Retrieved user data:`, result.Item);
    
    const balances = {
      accountBalance: result.Item?.accountBalance || 0,
      availableBalance: result.Item?.availableBalance || 0
    };
    
    console.log(`Parsed balances for user ${userID}:`, balances);
    return balances;
  } catch (error) {
    console.error(`Error getting balances for user ${userID}:`, error);
    throw error;
  }
};

