import { APIGatewayProxyHand<PERSON>, APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { OAuth2Client } from 'google-auth-library';
import { googleAuthSchema } from './validation';
import { getUserByEmail, getUserByGoogleId, createUser<PERSON>romGoogle, linkGoogleAccount, updateUser, getUserByPublicKey } from '../shared/database/userOperations';
import { User } from '../shared/types';

import { addCorsHeaders } from '../shared/corsHandler';
import { generateToken } from '../shared/utils/tokenUtils';
import { decrypt } from '../shared/utils/encryptionUtils';
import { getConfig } from '../shared/services/configService';
import { NotificationService } from '../shared/services/notificationService';

export const googleAuthLambda: APIGatewayProxyHandler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    if (event.httpMethod === 'OPTIONS') {
        return addCorsHeaders({
            statusCode: 200,
            body: '',
        }, event);
    }

    try {
        // Load configuration for environment detection
        const config = await getConfig(event);
        
        const body = JSON.parse(event.body || '{}');
        const { error } = googleAuthSchema.validate(body);
        if (error) {
            return addCorsHeaders({
                statusCode: 400,
                body: JSON.stringify({ message: 'Invalid input', details: error.details }),
            }, event);
        }

        const { idToken, parentPublicKey } = body;

        if (!parentPublicKey || typeof parentPublicKey !== 'string' || parentPublicKey.trim() === '') {
            return addCorsHeaders({
                statusCode: 400,
                body: JSON.stringify({ message: 'Missing or invalid parentPublicKey.' }),
            }, event);
        }
        // Format validation (same as sign-in)
        if (!parentPublicKey.match(/^APK_[a-f0-9]{32}_\d+$/)) {

            return addCorsHeaders({
                statusCode: 400,
                body: JSON.stringify({ message: 'Invalid parentPublicKey format.' }),
            }, event);
        }

        // Use the correct table name from config
        const tableName = config.USER_DETAILS_TABLE_NAME;
        const parent = await getUserByPublicKey(parentPublicKey, tableName);
        if (!parent) {
            return addCorsHeaders({
                statusCode: 400,
                body: JSON.stringify({ message: 'Invalid parent public key.' }),
            }, event);
        }
    
        
        let clientId = process.env.GOOGLE_CLIENT_ID;
        let clientSecret: string | undefined = undefined;

        if (parent.googleSsoConfig?.enabled && parent.googleSsoConfig?.clientId && parent.googleSsoConfig?.clientSecret) {
            clientId = parent.googleSsoConfig.clientId;
            clientSecret = decrypt(parent.googleSsoConfig.clientSecret);
        } else {
            // Optional: If you want to prevent login if parent SSO is not configured, you can uncomment the following lines
            // return addCorsHeaders({
            //     statusCode: 400,
            //     body: JSON.stringify({ message: 'Google Sign-In is not configured for this organization.' }),
            // }, event);
        }
        
        const client = new OAuth2Client(clientId);
        
        const ticket = await client.verifyIdToken({
            idToken,
            audience: clientId,
        });

        const payload = ticket.getPayload();
        if (!payload || !payload.sub || !payload.email) {
            return addCorsHeaders({
                statusCode: 400,
                body: JSON.stringify({ message: 'Invalid Google token.' }),
            }, event);
        }

        if(!payload.email_verified) {
            return addCorsHeaders({
                statusCode: 400,
                body: JSON.stringify({ message: 'Email not verified by Google.' }),
            }, event);
        }
        
        let user: User | null = await getUserByGoogleId(payload.sub, parentPublicKey, tableName);

        let isNewUser = false;
        if (!user) {
            const existingUser = await getUserByEmail(payload.email, tableName);

            if (existingUser) {
                user = await linkGoogleAccount(existingUser.userID, payload.sub, tableName);
                console.log('[GOOGLE] Linked Google account to existing user:', user?.userID);
            } else {
                user = await createUserFromGoogle({ sub: payload.sub, email: payload.email, name: payload.name || payload.email }, parentPublicKey, tableName);
                isNewUser = true;
                console.log('[GOOGLE] Created new user from Google:', user?.userID);
            }
        }

        if (user) {
            console.log('Calling updateUser with:', user.userID, { lastLogin: Date.now(), lastLoginProvider: 'google' }, tableName);
            try {
                await updateUser(user.userID, { lastLogin: Date.now(), lastLoginProvider: 'google' }, tableName);
                console.log('updateUser completed successfully');
            } catch (updateError) {
                console.error('Error updating user login info:', updateError);
                // Continue anyway - this is not critical for authentication
            }

            // Send Telegram notification for new user signups (non-blocking)
            if (isNewUser) {
                NotificationService.sendSignupNotification(
                    user.username || payload.name || 'Google User',
                    user.email,
                    event
                ).catch(err => {
                    console.error('[GOOGLE] Error sending signup notification:', err);
                });
            }
        }

        if (!user) {
            console.error('Failed to create or update user - user is null');
            return addCorsHeaders({
                statusCode: 500,
                body: JSON.stringify({ message: 'Failed to create or update user.' }),
            }, event);
        }

        console.log('About to generate token for user:', user.userID);
        let token;
        try {
            token = await generateToken(user, tableName);
            console.log('Token generated successfully, length:', token?.length || 0);
        } catch (tokenError) {
            console.error('Error generating token:', tokenError);
            return addCorsHeaders({
                statusCode: 500,
                body: JSON.stringify({ message: 'Failed to generate authentication token.' }),
            }, event);
        }

        const responseBody = {
            success: true,
            token,
            user: {
                userID: user.userID,
                email: user.email,
                username: user.username,
                publicKey: user.publicKey
            }
        };

        console.log('Preparing response:', {
            success: responseBody.success,
            hasToken: !!responseBody.token,
            tokenLength: responseBody.token?.length || 0,
            userID: responseBody.user.userID
        });

        const response = addCorsHeaders({
            statusCode: 200,
            body: JSON.stringify(responseBody),
        }, event);

        console.log('Final response prepared:', {
            statusCode: response.statusCode,
            hasBody: !!response.body,
            bodyLength: response.body?.length || 0,
            headers: Object.keys(response.headers || {})
        });

        console.log('About to return response - Google Auth Lambda completing successfully');
        return response;

    } catch (err) {
        console.error(err);
        const message = (err instanceof Error) ? err.message : 'An unknown error occurred';
        return addCorsHeaders({
            statusCode: 500,
            body: JSON.stringify({ message: 'Internal Server Error', error: message }),
        }, event);
    }
};   
