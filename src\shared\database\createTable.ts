import { CreateTableCommand, UpdateTimeToLiveCommand } from "@aws-sdk/client-dynamodb";
import { USER_TABLE_CONFIG } from "./tableConfig";
import { dynamoDBClient } from "../database";

const TABLE_NAME = process.env.USER_DETAILS_TABLE_NAME || 'userAuthentication';

async function enableTTL(tableName: string) {
  const ttlParams = {
    TableName: tableName,
    TimeToLiveSpecification: {
      AttributeName: 'OTPExpiry',
      Enabled: true
    }
  };

  try {
    await dynamoDBClient.send(new UpdateTimeToLiveCommand(ttlParams));
    console.log('TTL enabled Successfully');
  } catch (error) {
    console.error('Error enabling TTL:', error);
    throw error;
  }
}

async function createUserTable() {
  try {
    const response = await dynamoDBClient.send(new CreateTableCommand(USER_TABLE_CONFIG));
    console.log('Table created successfully:', response.TableDescription?.TableName);
    
    // Enable TTL after table creation
    await enableTTL(TABLE_NAME);
    
    return response;
  } catch (error) {
    if ((error as any).name === 'ResourceInUseException') {
      console.log('Table already exists');
    } else {
      console.error('Error creating table:', error);
      throw error;
    }
  }
}

if (require.main === module) {
  createUserTable()
    .then(() => console.log('Table creation process completed'))
    .catch(error => console.error('Failed to create table:', error));
}

export { createUserTable }; 