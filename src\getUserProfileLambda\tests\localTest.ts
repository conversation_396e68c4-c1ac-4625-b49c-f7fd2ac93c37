import { APIGatewayProxyEvent } from 'aws-lambda';
import { getUserProfileLambda } from '../handler';

/**
 * Local test file for getUserProfileLambda
 * 
 * To run this test:
 * 1. Set up your local environment variables
 * 2. Ensure you have a valid JWT token
 * 3. Run: npx ts-node src/getUserProfileLambda/tests/localTest.ts
 */

// Mock event for local testing
const createTestEvent = (token: string, isStaging: boolean = false): APIGatewayProxyEvent => ({
  headers: {
    Authorization: `Bearer ${token}`,
    Host: isStaging ? 'staging.api.authiqa.com' : 'api.authiqa.com',
    Origin: isStaging ? 'https://staging.authiqa.com' : 'https://authiqa.com'
  },
  body: null,
  httpMethod: 'GET',
  path: '/auth/user-profile',
  queryStringParameters: null,
  pathParameters: null,
  stageVariables: null,
  requestContext: {
    requestId: 'test-request-id',
    stage: isStaging ? 'staging' : 'prod',
    resourceId: 'test-resource-id',
    httpMethod: 'GET',
    requestTime: new Date().toISOString(),
    path: '/auth/user-profile',
    accountId: 'test-account-id',
    resourcePath: '/auth/user-profile',
    identity: {
      sourceIp: '127.0.0.1',
      userAgent: 'test-user-agent'
    } as any,
    apiId: 'test-api-id'
  } as any,
  resource: '/auth/user-profile',
  isBase64Encoded: false,
  multiValueHeaders: {},
  multiValueQueryStringParameters: null
});

async function testGetUserProfile() {
  console.log('🧪 Starting local test for getUserProfileLambda...\n');

  // Test cases
  const testCases = [
    {
      name: 'Missing Authorization Header',
      event: createTestEvent(''),
      expectedStatus: 401
    },
    {
      name: 'Invalid Token',
      event: createTestEvent('invalid-token'),
      expectedStatus: 401
    },
    // Add a valid token here for testing with real data
    // {
    //   name: 'Valid Token - Production',
    //   event: createTestEvent('your-valid-jwt-token-here'),
    //   expectedStatus: 200
    // },
    // {
    //   name: 'Valid Token - Staging',
    //   event: createTestEvent('your-valid-jwt-token-here', true),
    //   expectedStatus: 200
    // }
  ];

  for (const testCase of testCases) {
    console.log(`📋 Test: ${testCase.name}`);
    console.log('─'.repeat(50));

    try {
      // Remove Authorization header if token is empty
      if (!testCase.event.headers.Authorization?.replace('Bearer ', '')) {
        delete testCase.event.headers.Authorization;
      }

      const result = await getUserProfileLambda(testCase.event);
      
      console.log(`✅ Status Code: ${result.statusCode}`);
      console.log(`📄 Response Headers:`, JSON.stringify(result.headers, null, 2));
      
      const responseBody = JSON.parse(result.body);
      console.log(`📦 Response Body:`, JSON.stringify(responseBody, null, 2));
      
      // Check if status matches expected
      if (result.statusCode === testCase.expectedStatus) {
        console.log(`✅ Expected status ${testCase.expectedStatus} - PASSED`);
      } else {
        console.log(`❌ Expected status ${testCase.expectedStatus}, got ${result.statusCode} - FAILED`);
      }

      // Additional checks for successful responses
      if (result.statusCode === 200) {
        const user = responseBody.user;
        console.log(`👤 User Profile Summary:`);
        console.log(`   - User ID: ${user.userID}`);
        console.log(`   - Username: ${user.username}`);
        console.log(`   - Email: ${user.email}`);
        console.log(`   - Account Type: ${user.accountType}`);
        console.log(`   - Account Status: ${user.accountStatus}`);
        console.log(`   - Email Verified: ${user.emailVerified}`);
        console.log(`   - Organization: ${user.organizationName || 'None'}`);
        console.log(`   - Is Promoted: ${user.isPromotedAccount || false}`);
        
        if (user.accountType === 'parent') {
          console.log(`   - Account Balance: $${user.accountBalance || 0}`);
          console.log(`   - Available Balance: $${user.availableBalance || 0}`);
        }
        
        console.log(`   - Sign In Count: ${user.signInCount || 0}`);
        console.log(`   - Organization Updates: ${user.organizationUpdateCount || 0}`);
      }

    } catch (error) {
      console.error(`❌ Test failed with error:`, error);
    }

    console.log('\n' + '='.repeat(60) + '\n');
  }

  console.log('🏁 Local testing completed!');
}

// Environment setup check
function checkEnvironment() {
  console.log('🔧 Environment Check:');
  console.log(`   - NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
  console.log(`   - USER_DETAILS_TABLE_NAME: ${process.env.USER_DETAILS_TABLE_NAME || 'not set'}`);
  console.log(`   - JWT_SECRET: ${process.env.JWT_SECRET ? 'set' : 'not set'}`);
  console.log(`   - ENCRYPTION_KEY: ${process.env.ENCRYPTION_KEY ? 'set' : 'not set'}`);
  console.log(`   - AWS_REGION: ${process.env.AWS_REGION || 'not set'}`);
  console.log('');
}

// Instructions for manual testing
function printInstructions() {
  console.log('📝 Manual Testing Instructions:');
  console.log('');
  console.log('1. To test with a real token:');
  console.log('   - Sign in to get a valid JWT token');
  console.log('   - Replace "your-valid-jwt-token-here" in the test cases above');
  console.log('   - Uncomment the valid token test cases');
  console.log('');
  console.log('2. Environment variables needed:');
  console.log('   - USER_DETAILS_TABLE_NAME (or use SSM parameters)');
  console.log('   - JWT_SECRET');
  console.log('   - ENCRYPTION_KEY');
  console.log('   - AWS_REGION');
  console.log('');
  console.log('3. For staging tests:');
  console.log('   - Ensure staging SSM parameters are accessible');
  console.log('   - Use staging-specific JWT tokens');
  console.log('');
}

// Run the tests
async function main() {
  console.log('🚀 getUserProfileLambda Local Test Suite');
  console.log('=' .repeat(60));
  console.log('');
  
  checkEnvironment();
  printInstructions();
  
  await testGetUserProfile();
}

// Execute if run directly
if (require.main === module) {
  main().catch(console.error);
}

export { testGetUserProfile, createTestEvent };
