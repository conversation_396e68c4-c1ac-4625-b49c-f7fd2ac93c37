export class WebhookSignatureError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'WebhookSignatureError';
  }
}

export class PaymentProcessingError extends Error {
  constructor(message: string, public readonly stripeError?: any) {
    super(message);
    this.name = 'PaymentProcessingError';
  }
}

export class PaymentMetadataError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'PaymentMetadataError';
  }
} 