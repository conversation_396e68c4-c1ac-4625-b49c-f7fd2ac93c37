import * as jwt from 'jsonwebtoken';
import * as crypto from 'crypto';
import { AccountType } from '../types';
import { getUserByPublicKey } from '../database/userOperations';

// Base token interface
interface TokenUser {
  userID: string;
  email: string;
  username?: string;
  parentAccount?: string;
  accountType?: AccountType;
  publicKey?: string;
}

// Organization token interface
interface OrganizationTokenUser {
  userID: string;
  email: string;
  accountType: AccountType;
  parentAccount?: string;
  publicKey: string;
}

const ENCRYPTION_KEY = (() => {
  const key = Buffer.from(process.env.ENCRYPTION_KEY || '', 'base64');
  if (key.length !== 32) {
    console.error('Invalid encryption key length:', key.length, 'bytes. Expected 32 bytes.');
    throw new Error('Invalid encryption key configuration');
  }
  return key;
})();
const IV_LENGTH = 16;

export interface DecodedResetToken {
  email: string;
  otp: string;
  parentPublicKey?: string;
}

export const generateToken = async (user: TokenUser | OrganizationTokenUser, tableName?: string): Promise<string> => {
  // For parent accounts, use the global secret
  if (!user.accountType || user.accountType === 'parent') {
    console.log(`[TOKEN] Using global JWT secret for parent account: ${user.userID}`);
    return jwt.sign(
      user,
      process.env.JWT_SECRET || 'default_secret',
      { expiresIn: '24h' }
    );
  }
  
  // For child accounts, fetch parent's secret
  if (user.parentAccount && user.parentAccount !== 'ROOT') {
    console.log(`[TOKEN] Fetching parent JWT secret for child account: ${user.userID}, parent: ${user.parentAccount}`);
    const parentUser = await getUserByPublicKey(user.parentAccount, tableName);
    
    if (parentUser?.jwtSecret) {
      console.log(`[TOKEN] Using parent-specific JWT secret for child account: ${user.userID}`);
      return jwt.sign(
        user,
        parentUser.jwtSecret,
        { expiresIn: '24h' }
      );
    } else {
      console.log(`[TOKEN] Parent JWT secret not found for child account: ${user.userID}, falling back to global secret`);
    }
  }
  
  // Fallback to global secret if parent secret not found
  console.log(`[TOKEN] Using fallback global JWT secret for account: ${user.userID}`);
  return jwt.sign(
    user,
    process.env.JWT_SECRET || 'default_secret',
    { expiresIn: '24h' }
  );
};

export const generateRefreshToken = (user: TokenUser | OrganizationTokenUser): string => {
  return jwt.sign(
    {
      userID: user.userID,
    },
    process.env.REFRESH_TOKEN_SECRET || 'default_refresh_secret',
    { expiresIn: '7d' }
  );
};

export interface ResetToken {
  email: string;
  otp: string;
  parentPublicKey?: string;
}

export const generateResetToken = (email: string, otp: string, parentPublicKey?: string): string => {
  try {
    console.log('[TokenUtils] RESET TOKEN GENERATION START -----');
    
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipheriv('aes-256-cbc', ENCRYPTION_KEY, iv);
    const payload = JSON.stringify({ email, otp, parentPublicKey });
    
    let encrypted = cipher.update(payload, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    
    const token = `${iv.toString('base64')}.${encrypted}`;
    const finalToken = encodeURIComponent(token);
    
    console.log('[TokenUtils] RESET TOKEN GENERATION END -----');
    return finalToken;
  } catch (error) {
    console.error('[TokenUtils] Error generating reset token:', error);
    throw error;
  }
};

export const decryptResetToken = (token: string): ResetToken => {
  try {
    console.log('[TokenUtils] RESET TOKEN DECRYPTION START -----');
    console.log('[TokenUtils] Received token:', {
      tokenLength: token?.length,
      isEncoded: token !== decodeURIComponent(token)
    });
    
    const decodedToken = decodeURIComponent(token);
    console.log('[TokenUtils] Decoded URI token:', {
      decodedLength: decodedToken.length,
      containsDot: decodedToken.includes('.'),
      parts: decodedToken.split('.').length
    });
    
    const [ivString, encryptedData] = decodedToken.split('.');
    console.log('[TokenUtils] Token parts analysis:', {
      hasIV: !!ivString,
      ivLength: ivString?.length,
      hasEncryptedData: !!encryptedData,
      encryptedLength: encryptedData?.length,
      isValidFormat: !!(ivString && encryptedData)
    });

    if (!ivString || !encryptedData) {
      console.error('[TokenUtils] Token validation failed:', {
        hasIV: !!ivString,
        hasEncryptedData: !!encryptedData
      });
      throw new Error('Token is missing IV or encrypted data');
    }

    const iv = Buffer.from(ivString, 'base64');
    console.log('[TokenUtils] IV decoded:', {
      originalLength: ivString.length,
      decodedLength: iv.length,
      isValidLength: iv.length === IV_LENGTH
    });
    
    const decipher = crypto.createDecipheriv('aes-256-cbc', ENCRYPTION_KEY, iv);
    let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
    
    const payload = JSON.parse(decrypted);
    console.log('[TokenUtils] Decryption successful:', {
      hasEmail: !!payload.email,
      hasOTP: !!payload.otp,
      otpLength: payload.otp?.length
    });
    console.log('[TokenUtils] RESET TOKEN DECRYPTION END -----');
    
    return {
      email: payload.email,
      otp: payload.otp,
      parentPublicKey: payload.parentPublicKey
    };
  } catch (error) {
    console.error('[TokenUtils] Error decrypting reset token:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    if (error instanceof Error) {
      throw new Error(`Invalid reset token: ${error.message}`);
    }
    throw new Error('Invalid reset token');
  }
};

export async function verifyToken(token: string, tableName?: string): Promise<TokenUser | OrganizationTokenUser | null> {
  try {
    // First try to decode without verification to get the account type
    const decoded = jwt.decode(token) as TokenUser | OrganizationTokenUser;
    
    if (!decoded) {
      console.log(`[TOKEN-VERIFY] Failed to decode token`);
      return null;
    }
    
    console.log(`[TOKEN-VERIFY] Decoded token for user: ${decoded.userID}, account type: ${decoded.accountType || 'unknown'}`);
    
    let secret: string;
    
    if (!decoded.accountType || decoded.accountType === 'parent') {
      console.log(`[TOKEN-VERIFY] Using global JWT secret for parent account: ${decoded.userID}`);
      secret = process.env.JWT_SECRET || 'default_secret';
    } else if (decoded.parentAccount && decoded.parentAccount !== 'ROOT') {
      // For child accounts, get parent's secret
      console.log(`[TOKEN-VERIFY] Fetching parent JWT secret for child account: ${decoded.userID}, parent: ${decoded.parentAccount}`);
      const parentUser = await getUserByPublicKey(decoded.parentAccount, tableName);
      
      if (parentUser?.jwtSecret) {
        console.log(`[TOKEN-VERIFY] Using parent-specific JWT secret for child account: ${decoded.userID}`);
        secret = parentUser.jwtSecret;
      } else {
        console.log(`[TOKEN-VERIFY] Parent JWT secret not found, falling back to global secret for: ${decoded.userID}`);
        secret = process.env.JWT_SECRET || 'default_secret';
      }
    } else {
      console.log(`[TOKEN-VERIFY] Using fallback global JWT secret for account: ${decoded.userID}`);
      secret = process.env.JWT_SECRET || 'default_secret';
    }
    
    const verified = await jwt.verify(token, secret) as TokenUser | OrganizationTokenUser;
    console.log(`[TOKEN-VERIFY] Successfully verified token for user: ${verified.userID}`);
    return verified;
  } catch (error) {
    console.error(`[TOKEN-VERIFY] Error verifying token:`, error);
    return null;
  }
}

export function formatLastSeen(timestamp: number): string {
    if (!timestamp) return 'Never';
    
    const diff = Date.now() - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 60) return minutes <= 1 ? 'Just now' : `${minutes} minutes ago`;
    if (hours < 24) return hours === 1 ? '1 hour ago' : `${hours} hours ago`;
    if (days < 30) return days === 1 ? 'Yesterday' : `${days} days ago`;
    return new Date(timestamp).toLocaleDateString('en-US', { 
        day: 'numeric', 
        month: 'short', 
        year: 'numeric' 
    });
}

export function formatRegistrationDate(timestamp: number): string {
    return new Date(timestamp).toLocaleDateString('en-US', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
    });
}
  
