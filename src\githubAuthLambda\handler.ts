import { APIGatewayProxy<PERSON><PERSON><PERSON>, APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { githubAuthSchema } from './validation';
import { getUserByEmail, getUserByGithubId, createUserFromGithub, linkGithubAccount, updateUser, getUserByPublicKey } from '../shared/database/userOperations';
import { User } from '../shared/types';

import { addCorsHeaders } from '../shared/corsHandler';
import { generateToken } from '../shared/utils/tokenUtils';
import { decrypt } from '../shared/utils/encryptionUtils';
import { getConfig } from '../shared/services/configService';
import { NotificationService } from '../shared/services/notificationService';

export const githubAuthLambda: APIGatewayProxyHandler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    if (event.httpMethod === 'OPTIONS') {
        return addCorsHeaders({
            statusCode: 200,
            body: '',
        }, event);
    }

    try {
        console.log('[START] GitHub Auth Lambda invoked');
        // Load configuration for environment detection
        let config;
        try {
            config = await getConfig(event);
            console.log('[CONFIG] Loaded:', config);
        } catch (err) {
            console.error('[CONFIG] Failed to load config:', err);
            throw new Error('Failed to load configuration');
        }

        console.log('[HEADERS]', {
            host: event.headers.Host || event.headers.host,
            origin: event.headers.Origin || event.headers.origin
        });

        let body;
        try {
            body = JSON.parse(event.body || '{}');
            console.log('[BODY] Parsed:', body);
        } catch (err) {
            console.error('[BODY] Failed to parse body:', err);
            return addCorsHeaders({
                statusCode: 400,
                body: JSON.stringify({ message: 'Invalid JSON body' }),
            }, event);
        }

        const { error } = githubAuthSchema.validate(body);
        if (error) {
            console.warn('[VALIDATION] Input failed:', error.details);
            return addCorsHeaders({
                statusCode: 400,
                body: JSON.stringify({ message: 'Invalid input', details: error.details }),
            }, event);
        }

        const { code, parentPublicKey } = body;

        if (!parentPublicKey || typeof parentPublicKey !== 'string' || parentPublicKey.trim() === '') {
            console.warn('[VALIDATION] Missing or invalid parentPublicKey');
            return addCorsHeaders({
                statusCode: 400,
                body: JSON.stringify({ message: 'Missing or invalid parentPublicKey.' }),
            }, event);
        }

        if (!parentPublicKey.match(/^APK_[a-f0-9]{32}_\d+$/)) {
            console.warn('[VALIDATION] Invalid parentPublicKey format:', parentPublicKey);
            return addCorsHeaders({
                statusCode: 400,
                body: JSON.stringify({ message: 'Invalid parentPublicKey format.' }),
            }, event);
        }

        // Use the correct table name from config
        const tableName = config.USER_DETAILS_TABLE_NAME;
        let parent;
        try {
            parent = await getUserByPublicKey(parentPublicKey, tableName);
            console.log('[DB] Parent loaded:', !!parent);
        } catch (err) {
            console.error('[DB] Failed to get parent by public key:', err);
            return addCorsHeaders({
                statusCode: 500,
                body: JSON.stringify({ message: 'Failed to load parent organization.' }),
            }, event);
        }
        if (!parent) {
            console.warn('[DB] Invalid parent public key:', parentPublicKey);
            return addCorsHeaders({
                statusCode: 400,
                body: JSON.stringify({ message: 'Invalid parent public key.' }),
            }, event);
        }

        // Get GitHub OAuth credentials (fallback to env vars)
        let clientId = process.env.GITHUB_CLIENT_ID;
        let clientSecret: string | undefined = undefined;

        if (parent.githubSsoConfig?.enabled && parent.githubSsoConfig?.clientId && parent.githubSsoConfig?.clientSecret) {
            clientId = parent.githubSsoConfig.clientId;
            try {
                clientSecret = decrypt(parent.githubSsoConfig.clientSecret);
                console.log('[GITHUB SSO] Using org credentials');
                // After decryption, before sending to GitHub
                console.log('[DEBUG] Decrypted clientSecret (first 6 chars):', clientSecret?.slice(0, 6));
            } catch (err) {
                console.error('[GITHUB SSO] Failed to decrypt clientSecret:', err);
                return addCorsHeaders({
                    statusCode: 500,
                    body: JSON.stringify({ message: 'Failed to decrypt GitHub SSO credentials.' }),
                }, event);
            }
        } else {
            console.log('[GITHUB SSO] Using environment credentials');
        }

        // Exchange code for access token
        let tokenData;
        try {
            const tokenResponse = await fetch('https://github.com/login/oauth/access_token', {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    client_id: clientId,
                    client_secret: clientSecret,
                    code: code,
                }),
            });
            tokenData = await tokenResponse.json();
            console.log('[GITHUB] Token exchange response:', tokenData);
        } catch (err) {
            console.error('[GITHUB] Failed to exchange code for token:', err);
            return addCorsHeaders({
                statusCode: 500,
                body: JSON.stringify({ message: 'Failed to exchange code for GitHub token.' }),
            }, event);
        }
        if (tokenData.error) {
            console.warn('[GITHUB] Invalid authorization code:', tokenData.error);
            return addCorsHeaders({
                statusCode: 400,
                body: JSON.stringify({ message: 'Invalid GitHub authorization code.' }),
            }, event);
        }

        // Get user info from GitHub
        let githubUser;
        try {
            const userResponse = await fetch('https://api.github.com/user', {
                headers: {
                    'Authorization': `token ${tokenData.access_token}`,
                    'User-Agent': 'Authiqa-App',
                },
            });
            githubUser = await userResponse.json();
            console.log('[GITHUB] User info:', githubUser);
        } catch (err) {
            console.error('[GITHUB] Failed to fetch user info:', err);
            return addCorsHeaders({
                statusCode: 500,
                body: JSON.stringify({ message: 'Failed to fetch user info from GitHub.' }),
            }, event);
        }
        if (!githubUser.id) {
            console.warn('[GITHUB] No user id in response');
            return addCorsHeaders({
                statusCode: 400,
                body: JSON.stringify({ message: 'Unable to retrieve user information from GitHub.' }),
            }, event);
        }

        // Get user's primary email if not public
        let userEmail = githubUser.email;
        if (!userEmail) {
            try {
                const emailResponse = await fetch('https://api.github.com/user/emails', {
                    headers: {
                        'Authorization': `token ${tokenData.access_token}`,
                        'User-Agent': 'Authiqa-App',
                    },
                });
                const emails = await emailResponse.json();
                const primaryEmail = emails.find((email: any) => email.primary && email.verified);
                if (!primaryEmail) {
                    console.warn('[GITHUB] No verified primary email found');
                    return addCorsHeaders({
                        statusCode: 400,
                        body: JSON.stringify({ message: 'GitHub account must have a verified primary email address.' }),
                    }, event);
                }
                userEmail = primaryEmail.email;
                console.log('[GITHUB] Primary email found:', userEmail);
            } catch (err) {
                console.error('[GITHUB] Failed to fetch user emails:', err);
                return addCorsHeaders({
                    statusCode: 500,
                    body: JSON.stringify({ message: 'Failed to fetch user emails from GitHub.' }),
                }, event);
            }
        }

        let user: User | null = null;
        try {
            user = await getUserByGithubId(githubUser.id.toString(), parentPublicKey, tableName);
            console.log('[DB] User by GitHub ID:', !!user);
        } catch (err) {
            console.error('[DB] Failed to get user by GitHub ID:', err);
        }

        let isNewUser = false;
        if (!user) {
            try {
                const existingUser = await getUserByEmail(userEmail, tableName);
                console.log('[DB] Existing user by email:', !!existingUser);

                if (existingUser) {
                    user = await linkGithubAccount(existingUser.userID, githubUser.id.toString(), tableName);
                    console.log('[DB] Linked GitHub account to existing user:', user?.userID);
                } else {
                    user = await createUserFromGithub({
                        id: githubUser.id.toString(),
                        email: userEmail,
                        login: githubUser.login,
                        name: githubUser.name || githubUser.login
                    }, parentPublicKey, tableName);
                    isNewUser = true;
                    console.log('[DB] Created new user from GitHub:', user?.userID);
                }
            } catch (err) {
                console.error('[DB] Failed to create/link user:', err);
                return addCorsHeaders({
                    statusCode: 500,
                    body: JSON.stringify({ message: 'Failed to create or link user.' }),
                }, event);
            }
        }

        if (user) {
            try {
                await updateUser(user.userID, { lastLogin: Date.now(), lastLoginProvider: 'github' }, tableName);
                console.log('[DB] Updated user login info:', user.userID);
            } catch (updateError) {
                console.error('[DB] Error updating user login info:', updateError);
            }

            // Send Telegram notification for new user signups (non-blocking)
            if (isNewUser) {
                NotificationService.sendSignupNotification(
                    user.username || githubUser.name || githubUser.login || 'GitHub User',
                    user.email,
                    event
                ).catch(err => {
                    console.error('[GITHUB] Error sending signup notification:', err);
                });
            }
        }

        if (!user) {
            console.error('[DB] Failed to create or update user - user is null');
            return addCorsHeaders({
                statusCode: 500,
                body: JSON.stringify({ message: 'Failed to create or update user.' }),
            }, event);
        }

        let token;
        try {
            token = await generateToken(user, tableName);
            console.log('[TOKEN] Generated successfully, length:', token?.length || 0);
        } catch (tokenError) {
            console.error('[TOKEN] Error generating token:', tokenError);
            return addCorsHeaders({
                statusCode: 500,
                body: JSON.stringify({ message: 'Failed to generate authentication token.' }),
            }, event);
        }

        const responseBody = {
            success: true,
            token,
            user: {
                userID: user.userID,
                email: user.email,
                username: user.username,
                publicKey: user.publicKey
            }
        };

        console.log('[RESPONSE] Success:', {
            userID: user.userID,
            tokenLength: token?.length || 0
        });

        const response = addCorsHeaders({
            statusCode: 200,
            body: JSON.stringify(responseBody),
        }, event);

        console.log('[END] Lambda completed successfully');
        return response;

    } catch (err) {
        console.error('[FATAL] Lambda error:', err);
        const message = (err instanceof Error) ? err.message : 'An unknown error occurred';
        return addCorsHeaders({
            statusCode: 500,
            body: JSON.stringify({ message: 'Internal Server Error', error: message }),
        }, event);
    }
};