// This file handles cors For Aws Lambda

import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { corsOptions } from './config/cors.config';

export const getCorsHeaders = (origin?: string): { [key: string]: string } => {
  return {
    'Access-Control-Allow-Origin': origin || '*',
    'Access-Control-Allow-Methods': Array.isArray(corsOptions.methods) ? corsOptions.methods.join(',') : '',
    'Access-Control-Allow-Headers': Array.isArray(corsOptions.allowedHeaders) ? corsOptions.allowedHeaders.join(',') : '',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': corsOptions.maxAge?.toString() || '86400',
    'Access-Control-Expose-Headers': Array.isArray(corsOptions.exposedHeaders) ? corsOptions.exposedHeaders.join(',') : ''
  };
};

export const addCorsHeaders = (
  response: APIGatewayProxyResult, 
  event?: APIGatewayProxyEvent
): APIGatewayProxyResult => {
  const origin = event?.headers?.origin || event?.headers?.Origin;
  return {
    ...response,
    headers: {
      ...response.headers,
      ...getCorsHeaders(origin)
    }
  };
}; 



