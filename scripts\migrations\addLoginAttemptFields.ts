import { DynamoDB } from 'aws-sdk';
import { ScanCommand, UpdateCommand } from "@aws-sdk/lib-dynamodb";

const dynamoDB = new DynamoDB.DocumentClient({
  region: process.env.AWS_REGION || 'eu-west-1'
});
const TABLE_NAME = process.env.USER_DETAILS_TABLE_NAME || 'userAuthentication';

async function migrateLoginAttemptFields() {
  try {
    console.log('Starting migration for login attempt fields...');
    
    const scanParams = {
      TableName: TABLE_NAME
    };
    
    const result = await dynamoDB.scan(scanParams).promise();
    const users = result.Items || [];
    
    for (const user of users) {
      if (user.loginAttempts === undefined) {
        await dynamoDB.update({
          TableName: TABLE_NAME,
          Key: { userID: user.userID },
          UpdateExpression: 'SET loginAttempts = :zero, lastLoginAttempt = :zero, lockedUntil = :nil',
          ExpressionAttributeValues: {
            ':zero': 0,
            ':nil': null
          }
        }).promise();
        console.log(`Updated user: ${user.userID}`);
      }
    }
    
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  migrateLoginAttemptFields()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}
