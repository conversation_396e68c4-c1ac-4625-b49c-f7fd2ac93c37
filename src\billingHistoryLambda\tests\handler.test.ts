import { APIGatewayProxyEvent } from 'aws-lambda';
import { billingHistoryLambda } from '../handler';
import { verifyToken } from '../../shared/utils/tokenUtils';
import { 
    createMockEvent, 
    createTestUser,
    expectErrorResponse,
    expectSuccessResponse
} from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, initializeTestDatabase, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { 
    seedBillingRecords,
    clearBillingTestData
} from '../../shared/testUtils/billingTestUtils';
// Add this import at the top with other imports
import { getTestUser } from '../../shared/testUtils/databaseTestUtils';
// Add at the top of the file
jest.mock('../../shared/utils/tokenUtils');

describe('Billing History Lambda Handler', () => {
    beforeAll(async () => {
        process.env.BILLING_TABLE_NAME = 'authiqaBilling';
        process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
        process.env.ENCRYPTION_KEY = "dBG9kF7bH5cJ2mN8pQ4sT6wX0yZ3vA1eI/nL+uM9oR4=";
        process.env.JWT_SECRET = "test-secret-key";
        await initializeTestDatabase();
    });

    afterAll(() => {
        delete process.env.BILLING_TABLE_NAME;
        delete process.env.USER_DETAILS_TABLE_NAME;
        delete process.env.ENCRYPTION_KEY;
        delete process.env.JWT_SECRET;
    });

  

    // Add the setupTestUser helper
    const setupTestUser = async (username: string) => {
        const user = await createTestUser({
            username,
            email: `${username}@example.com`,
            accountType: 'parent',
            parentAccount: 'ROOT'
        });
        await seedTestUser(user);
        
        (verifyToken as jest.Mock).mockReturnValue({
            userID: user.userID,
            email: user.email,
            accountType: 'parent',
            publicKey: user.publicKey
        });
        
        return user;
    };

    const createEventWithAuth = (path: string, queryParams = {}) => {
        const event = createMockEvent({}, path);
        event.headers = {
            ...event.headers,
            Authorization: 'Bearer valid-token'
        };
        if (Object.keys(queryParams).length > 0) {
            event.queryStringParameters = queryParams;
        }
        return event;
    };
    // First test case
    it.skip('should retrieve billing history successfully', async () => {
        const parentUser = await setupTestUser('parentuser');
    // Create actual billing records in the database
        await seedBillingRecords([
            {
                publicKey: parentUser.publicKey,
                monthYear: '2024-01',
                totalAccounts: 5,
                costAssociatedWithAccounts: 0.15,
                totalIOInteractions: 1000,
                costAssociatedWithIO: 0.25,
                totalFinalCost: 0.40,
                timestamp: Date.now()
            },
            {
                publicKey: parentUser.publicKey,
                monthYear: '2023-12',
                totalAccounts: 4,
                costAssociatedWithAccounts: 0.12,
                totalIOInteractions: 800,
                costAssociatedWithIO: 0.20,
                totalFinalCost: 0.32,
                timestamp: Date.now() - 30 * 24 * 60 * 60 * 1000
            }
        ]);
    const event = createEventWithAuth('/parent/billing-history');
        const response = await billingHistoryLambda(event as APIGatewayProxyEvent);
    expectSuccessResponse(response, 200, {
            billingRecords: expect.arrayContaining([
                expect.objectContaining({
                    monthYear: '2024-01',
                    totalFinalCost: 0.40
                }),
                expect.objectContaining({
                    monthYear: '2023-12',
                    totalFinalCost: 0.32
                })
            ]),
            summary: {
                totalCost: 0.72,
                averageMonthlySpend: 0.36
            }
        });
    // Verify the actual database state
        const updatedUser = await getTestUser(parentUser.userID);
        expect(updatedUser?.publicKey).toBe(parentUser.publicKey);
    });
    it.skip('should reject requests without authorization header', async () => {
        const event = createMockEvent({}, '/parent/billing-history');
        const response = await billingHistoryLambda(event as APIGatewayProxyEvent);
        
        expectErrorResponse(response, 401, 'UNAUTHORIZED', 'Missing authorization token');
    });
    it.skip('should reject non-parent accounts', async () => {
        (verifyToken as jest.Mock).mockReturnValue({
            userID: 'child-user-id',
            email: '<EMAIL>',
            accountType: 'child',
            publicKey: 'child-public-key'
        });
    const event = createEventWithAuth('/parent/billing-history');
        const response = await billingHistoryLambda(event as APIGatewayProxyEvent);
        
        expectErrorResponse(response, 401, 'UNAUTHORIZED', 'Only parent accounts can access billing history');
    });
    // For date validation tests, we need to set parent token
    it.skip('should validate start date format', async () => {
        const parentUser = await setupTestUser('datevalidationuser');
        const event = createEventWithAuth('/parent/billing-history', {
            startDate: '2024/01'
        });
        
        const response = await billingHistoryLambda(event as APIGatewayProxyEvent);
        expectErrorResponse(response, 400, 'INVALID_DATE_FORMAT', 'Start date must be in YYYY-MM format');
    });
    // Add token verification for end date validation test
    it.skip('should validate end date format', async () => {
        (verifyToken as jest.Mock).mockReturnValue({
            userID: 'test-user-id',
            email: '<EMAIL>',
            accountType: 'parent',
            publicKey: 'test-public-key'
        });
        const event = createEventWithAuth('/parent/billing-history', {
            endDate: '01-2024'
        });
        
        const response = await billingHistoryLambda(event as APIGatewayProxyEvent);
        expectErrorResponse(response, 400, 'INVALID_DATE_FORMAT', 'End date must be in YYYY-MM format');
    });
    it.skip('should filter billing history by date range', async () => {
            const parentUser = await setupTestUser('daterangeuser');
    
            // Create billing records across multiple months
            await seedBillingRecords([
                {
                    publicKey: parentUser.publicKey,
                    monthYear: '2024-01',
                    totalAccounts: 5,
                    costAssociatedWithAccounts: 0.15,
                    totalIOInteractions: 1000,
                    costAssociatedWithIO: 0.25,
                    totalFinalCost: 0.40,
                    timestamp: Date.now()
                },
                {
                    publicKey: parentUser.publicKey,
                    monthYear: '2023-12',
                    totalAccounts: 4,
                    costAssociatedWithAccounts: 0.12,
                    totalIOInteractions: 800,
                    costAssociatedWithIO: 0.20,
                    totalFinalCost: 0.32,
                    timestamp: Date.now() - 30 * 24 * 60 * 60 * 1000
                },
                {
                    publicKey: parentUser.publicKey,
                    monthYear: '2023-11',
                    totalAccounts: 3,
                    costAssociatedWithAccounts: 0.09,
                    totalIOInteractions: 600,
                    costAssociatedWithIO: 0.15,
                    totalFinalCost: 0.24,
                    timestamp: Date.now() - 60 * 24 * 60 * 60 * 1000
                }
            ]);
    
            const event = createEventWithAuth('/parent/billing-history', {
                startDate: '2023-12',
                endDate: '2024-01'
            });
    
            const response = await billingHistoryLambda(event as APIGatewayProxyEvent);
    
            expectSuccessResponse(response, 200, {
                billingRecords: expect.arrayContaining([
                    expect.objectContaining({
                        monthYear: '2024-01',
                        totalFinalCost: 0.40
                    }),
                    expect.objectContaining({
                        monthYear: '2023-12',
                        totalFinalCost: 0.32
                    })
                ]),
                summary: {
                    totalCost: 0.72,
                    averageMonthlySpend: 0.36
                }
            });
        });
    it.skip('should handle limit parameter correctly', async () => {
        const parentUser = await setupTestUser('parentuser');
    
        // Create multiple billing records
        await seedBillingRecords([
            {
                publicKey: parentUser.publicKey,
                monthYear: '2024-01',
                totalAccounts: 5,
                costAssociatedWithAccounts: 0.15,
                totalIOInteractions: 1000,
                costAssociatedWithIO: 0.25,
                totalFinalCost: 0.40,
                timestamp: Date.now()
            },
            {
                publicKey: parentUser.publicKey,
                monthYear: '2023-12',
                totalAccounts: 4,
                costAssociatedWithAccounts: 0.12,
                totalIOInteractions: 800,
                costAssociatedWithIO: 0.20,
                totalFinalCost: 0.32,
                timestamp: Date.now() - 30 * 24 * 60 * 60 * 1000
            }
        ]);
    
        const event = createEventWithAuth('/parent/billing-history', {
            limit: '1'
        });
    
        const response = await billingHistoryLambda(event as APIGatewayProxyEvent);
        
        expectSuccessResponse(response, 200, {
            billingRecords: expect.arrayContaining([
                expect.objectContaining({
                    monthYear: '2024-01',
                    totalFinalCost: 0.40
                })
            ]),
            summary: {
                totalCost: 0.40,
                averageMonthlySpend: 0.40
            }
        });
    });
    it.skip('should filter with only start date', async () => {
        const parentUser = await setupTestUser('startdateuser');
    
        await seedBillingRecords([
            {
                publicKey: parentUser.publicKey,
                monthYear: '2024-01',
                totalAccounts: 5,
                costAssociatedWithAccounts: 0.15,
                totalIOInteractions: 1000,
                costAssociatedWithIO: 0.25,
                totalFinalCost: 0.40,
                timestamp: Date.now()
            },
            {
                publicKey: parentUser.publicKey,
                monthYear: '2023-11',
                totalAccounts: 3,
                costAssociatedWithAccounts: 0.09,
                totalIOInteractions: 600,
                costAssociatedWithIO: 0.15,
                totalFinalCost: 0.24,
                timestamp: Date.now() - 60 * 24 * 60 * 60 * 1000
            }
        ]);
    
        const event = createEventWithAuth('/parent/billing-history', {
            startDate: '2023-12'
        });
    
        const response = await billingHistoryLambda(event as APIGatewayProxyEvent);
    
        expectSuccessResponse(response, 200, {
            billingRecords: expect.arrayContaining([
                expect.objectContaining({
                    monthYear: '2024-01',
                    totalFinalCost: 0.40
                })
            ]),
            summary: {
                totalCost: 0.40,
                averageMonthlySpend: 0.40
            }
        });
    });
    
    // Add token verification for date range validation test
    it.skip('should reject when end date is before start date', async () => {
        const parentUser = await setupTestUser('daterangeuser');
        const event = createEventWithAuth('/parent/billing-history', {
            startDate: '2024-01',
            endDate: '2023-12'
        });
        
        const response = await billingHistoryLambda(event as APIGatewayProxyEvent);
        expectErrorResponse(response, 400, 'INVALID_DATE_RANGE', 'End date cannot be before start date');
    });

    // Add token verification for future dates test
    it.skip('should handle future dates appropriately', async () => {
        const parentUser = await setupTestUser('futuredatesuser');
        const event = createEventWithAuth('/parent/billing-history', {
            startDate: '2025-01',
            endDate: '2025-12'
        });
        
        const response = await billingHistoryLambda(event as APIGatewayProxyEvent);
        expectSuccessResponse(response, 200, {
            billingRecords: [],
            summary: {
                totalCost: 0,
                averageMonthlySpend: 0
            }
        });
    });
    it.skip('should handle empty billing history', async () => {
        const parentUser = await setupTestUser('emptyhistoryuser');
    
        const event = createEventWithAuth('/parent/billing-history');
        const response = await billingHistoryLambda(event as APIGatewayProxyEvent);
    
        expectSuccessResponse(response, 200, {
            billingRecords: [],
            summary: {
                totalCost: 0,
                averageMonthlySpend: 0
            }
        });
    });
});