{"total": {"lines": {"total": 1098, "covered": 131, "skipped": 0, "pct": 11.93}, "statements": {"total": 1195, "covered": 155, "skipped": 0, "pct": 12.97}, "functions": {"total": 206, "covered": 13, "skipped": 0, "pct": 6.31}, "branches": {"total": 515, "covered": 45, "skipped": 0, "pct": 8.73}, "branchesTrue": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\server.ts": {"lines": {"total": 97, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 20, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 106, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 25, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\emailConfirmationLambda\\handler.ts": {"lines": {"total": 32, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 34, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\emailConfirmationLambda\\validation.ts": {"lines": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\getChildAccountsLambda\\handler.ts": {"lines": {"total": 37, "covered": 31, "skipped": 0, "pct": 83.78}, "functions": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "statements": {"total": 40, "covered": 34, "skipped": 0, "pct": 85}, "branches": {"total": 21, "covered": 19, "skipped": 0, "pct": 90.47}}, "D:\\Natuvea\\SignInSignUp\\src\\getOrganizationDetailsLambda\\handler.ts": {"lines": {"total": 21, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 23, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 24, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\resendConfirmationEmailLambda\\handler.ts": {"lines": {"total": 50, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 52, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 11, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\resendConfirmationEmailLambda\\validation.ts": {"lines": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\resetPasswordLambda\\handler.ts": {"lines": {"total": 71, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 74, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 37, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\apiErrorHandling.ts": {"lines": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\auditLog.ts": {"lines": {"total": 10, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 11, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\config.ts": {"lines": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}, "branches": {"total": 4, "covered": 3, "skipped": 0, "pct": 75}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\constants.ts": {"lines": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\corsHandler.ts": {"lines": {"total": 6, "covered": 4, "skipped": 0, "pct": 66.66}, "functions": {"total": 2, "covered": 1, "skipped": 0, "pct": 50}, "statements": {"total": 8, "covered": 6, "skipped": 0, "pct": 75}, "branches": {"total": 32, "covered": 9, "skipped": 0, "pct": 28.12}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\database.ts": {"lines": {"total": 9, "covered": 9, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 9, "covered": 9, "skipped": 0, "pct": 100}, "branches": {"total": 4, "covered": 3, "skipped": 0, "pct": 75}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\emailService.ts": {"lines": {"total": 47, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 10, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 52, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 29, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\errorHandling.ts": {"lines": {"total": 28, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 11, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 28, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 7, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\errorTypes.ts": {"lines": {"total": 51, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 46, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 51, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\logger.ts": {"lines": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\loginAttempts.ts": {"lines": {"total": 21, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 23, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\otp.ts": {"lines": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\responseUtils.ts": {"lines": {"total": 8, "covered": 8, "skipped": 0, "pct": 100}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 10, "covered": 10, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\testUtils.ts": {"lines": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\utils.ts": {"lines": {"total": 22, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 23, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\validation.ts": {"lines": {"total": 21, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 25, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\auth\\jwt.ts": {"lines": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\auth\\passwordUtils.ts": {"lines": {"total": 7, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 10, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\auth\\passwords.ts": {"lines": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\config\\cors.config.ts": {"lines": {"total": 14, "covered": 4, "skipped": 0, "pct": 28.57}, "functions": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 15, "covered": 5, "skipped": 0, "pct": 33.33}, "branches": {"total": 27, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\constants\\auth.ts": {"lines": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\database\\createTable.ts": {"lines": {"total": 24, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 24, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\database\\tableConfig.ts": {"lines": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "branches": {"total": 2, "covered": 1, "skipped": 0, "pct": 50}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\database\\userOperations.ts": {"lines": {"total": 109, "covered": 39, "skipped": 0, "pct": 35.77}, "functions": {"total": 24, "covered": 3, "skipped": 0, "pct": 12.5}, "statements": {"total": 121, "covered": 46, "skipped": 0, "pct": 38.01}, "branches": {"total": 64, "covered": 9, "skipped": 0, "pct": 14.06}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\services\\environment.ts": {"lines": {"total": 17, "covered": 2, "skipped": 0, "pct": 11.76}, "functions": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 19, "covered": 3, "skipped": 0, "pct": 15.78}, "branches": {"total": 16, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\utils\\apiKeyUtils.ts": {"lines": {"total": 7, "covered": 6, "skipped": 0, "pct": 85.71}, "functions": {"total": 2, "covered": 1, "skipped": 0, "pct": 50}, "statements": {"total": 7, "covered": 6, "skipped": 0, "pct": 85.71}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\utils\\idGenerator.ts": {"lines": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\utils\\passwordUtils.ts": {"lines": {"total": 6, "covered": 5, "skipped": 0, "pct": 83.33}, "functions": {"total": 4, "covered": 2, "skipped": 0, "pct": 50}, "statements": {"total": 10, "covered": 8, "skipped": 0, "pct": 80}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\utils\\tokenUtils.ts": {"lines": {"total": 68, "covered": 14, "skipped": 0, "pct": 20.58}, "functions": {"total": 8, "covered": 1, "skipped": 0, "pct": 12.5}, "statements": {"total": 77, "covered": 19, "skipped": 0, "pct": 24.67}, "branches": {"total": 45, "covered": 1, "skipped": 0, "pct": 2.22}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\validation\\organizationValidation.ts": {"lines": {"total": 34, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 34, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 15, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\validation\\userValidation.ts": {"lines": {"total": 17, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 21, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\shared\\verification\\tokenGenerator.ts": {"lines": {"total": 10, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 13, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\signInLambda\\handler.ts": {"lines": {"total": 46, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 47, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 22, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\signInLambda\\validation.ts": {"lines": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 10, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\signUpLambda\\handler.ts": {"lines": {"total": 71, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 73, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 30, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\signUpLambda\\validation.ts": {"lines": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\updateOrganizationLambda\\handler.ts": {"lines": {"total": 43, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 45, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 13, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Natuvea\\SignInSignUp\\src\\updateOrganizationLambda\\validation.ts": {"lines": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Natuvea\\SignInSignUp\\src\\updatePasswordLambda\\handler.ts": {"lines": {"total": 47, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 49, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 21, "covered": 0, "skipped": 0, "pct": 0}}}