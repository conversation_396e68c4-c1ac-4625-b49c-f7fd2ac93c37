import { APIGatewayProxyEvent, Context, Callback, APIGatewayProxyResult } from 'aws-lambda';
import { updatePasswordLambda } from '../handler';
import { generateOTP } from '../../shared/otp';
import { generateResetToken } from '../../shared/utils/tokenUtils';
import { 
  createMockEvent, 
  createMockContext,
  createTestUser,
  expectErrorResponse,
  expectSuccessResponse
} from '../../shared/testUtils/lambdaTestUtils';
import { clearTestData, initializeTestDatabase, seedTestUser } from '../../shared/testUtils/databaseLifecycle';
import { getTestUser } from '../../shared/testUtils/databaseTestUtils';
import { generatePublicKey } from '../../shared/utils/apiKeyUtils';

jest.mock('../../shared/utils/tokenUtils');
jest.mock('../../shared/auth/passwordUtils');

describe('Update Password Lambda Handler', () => {
  const mockContext = createMockContext();
  const callback: Callback<APIGatewayProxyResult> = (error, result) => {};

  beforeEach(async () => {
    await clearTestData();
    jest.clearAllMocks();
    process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
    
    jest.spyOn(require('../../shared/auth/passwordUtils'), 'hashPassword')
      .mockResolvedValue('mocked-hashed-password');
  });

  afterEach(() => {
    delete process.env.USER_DETAILS_TABLE_NAME;
  });

  beforeAll(() => {
    process.env.ENCRYPTION_KEY = "dBG9kF7bH5cJ2mN8pQ4sT6wX0yZ3vA1eI/nL+uM9oR4=";
  });

  afterAll(() => {
    delete process.env.ENCRYPTION_KEY;
  });

  
  it.skip('should return error for missing token', async () => {
    const event = createMockEvent({
      password: 'NewStrongP@ssw0rd123'
    }, '/update-password');

    const response = await updatePasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'TOKEN_NOT_PROVIDED', 'Reset token is required');
  });

  it.skip('should return error for invalid token', async () => {
    jest.spyOn(require('../../shared/utils/tokenUtils'), 'decryptResetToken')
      .mockImplementation(() => { throw new Error('Invalid token'); });

    const event = createMockEvent({
      token: 'invalid-token',
      password: 'NewStrongP@ssw0rd123'
    }, '/update-password');

    const response = await updatePasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_TOKEN', 'Invalid or malformed reset token');
  });

  it.skip('should return error for expired OTP', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      resetPasswordOTP: '654321',
      resetPasswordOTPExpiry: Date.now() - 3600000 // expired 1 hour ago
    });
    
    await seedTestUser(testUser);
    const mockDecodedToken = {
      email: testUser.email,
      otp: '654321'
    };

    jest.spyOn(require('../../shared/utils/tokenUtils'), 'decryptResetToken')
      .mockReturnValue(mockDecodedToken);

    const event = createMockEvent({
      token: 'valid-token',
      password: 'NewStrongP@ssw0rd123'
    }, '/update-password');

    const response = await updatePasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'OTP_EXPIRED', 'Reset code has expired');
  });

  it.skip('should return error for weak password', async () => {
    const event = createMockEvent({
      token: 'valid-token',
      password: 'weak'
    }, '/update-password');

    const response = await updatePasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'INVALID_PASSWORD_FORMAT', 
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');
  });

  it.skip('should update password successfully for parent account', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      resetPasswordOTP: '654321',
      resetPasswordOTPExpiry: Date.now() + 3600000,
      accountType: 'parent',
      parentAccount: 'ROOT'
    });
    
    await seedTestUser(testUser);
    const mockDecodedToken = {
      email: testUser.email,
      otp: '654321'
    };

    jest.spyOn(require('../../shared/utils/tokenUtils'), 'decryptResetToken')
      .mockReturnValue(mockDecodedToken);

    const event = createMockEvent({
      token: 'valid-token',
      password: 'NewStrongP@ssw0rd123'
    }, '/update-password');

    const response = await updatePasswordLambda(event as APIGatewayProxyEvent);
    expectSuccessResponse(response, 200, {
      message: 'Password updated successfully'
    });
  });

  it.skip('should update password successfully for child account', async () => {
    const parentpublicKey = generatePublicKey();
    const parentUser = await createTestUser({
      username: 'parentuser',
      email: '<EMAIL>',
      parentAccount: 'ROOT',
      accountType: 'parent',
      publicKey: parentpublicKey
    });
    await seedTestUser(parentUser);

    const testUser = await createTestUser({
      emailVerified: true,
      resetPasswordOTP: '654321',
      resetPasswordOTPExpiry: Date.now() + 3600000,
      accountType: 'child',
      parentAccount: parentpublicKey
    });
    await seedTestUser(testUser);

    const mockDecodedToken = {
      email: testUser.email,
      otp: '654321',
      parentpublicKey: parentpublicKey
    };

    jest.spyOn(require('../../shared/utils/tokenUtils'), 'decryptResetToken')
      .mockReturnValue(mockDecodedToken);

    const event = createMockEvent({
      token: 'valid-token',
      password: 'NewStrongP@ssw0rd123'
    }, '/update-password');

    const response = await updatePasswordLambda(event as APIGatewayProxyEvent);
    expectSuccessResponse(response, 200, {
      message: 'Password updated successfully'
    });
  });

  it.skip('should return error if child account token missing parentpublicKey', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      resetPasswordOTP: '654321',
      resetPasswordOTPExpiry: Date.now() + 3600000,
      accountType: 'child',
      parentAccount: generatePublicKey()
    });
    await seedTestUser(testUser);

    const mockDecodedToken = {
      email: testUser.email,
      otp: '654321'
      // Missing parentpublicKey
    };

    jest.spyOn(require('../../shared/utils/tokenUtils'), 'decryptResetToken')
      .mockReturnValue(mockDecodedToken);

    const event = createMockEvent({
      token: 'valid-token',
      password: 'NewStrongP@ssw0rd123'
    }, '/update-password');

    const response = await updatePasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 400, 'MISSING_PARENT_PUBLIC_KEY', 'Parent public key is required for child accounts');
  });

  it.skip('should return error if child account token has invalid parentpublicKey', async () => {
    const correctParentpublicKey =  generatePublicKey();
    const wrongParentpublicKey = generatePublicKey();
    
    const testUser = await createTestUser({
      emailVerified: true,
      resetPasswordOTP: '654321',
      resetPasswordOTPExpiry: Date.now() + 3600000,
      accountType: 'child',
      parentAccount: correctParentpublicKey
    });
    await seedTestUser(testUser);

    const mockDecodedToken = {
      email: testUser.email,
      otp: '654321',
      parentpublicKey: wrongParentpublicKey
    };

    jest.spyOn(require('../../shared/utils/tokenUtils'), 'decryptResetToken')
      .mockReturnValue(mockDecodedToken);

    const event = createMockEvent({
      token: 'valid-token',
      password: 'NewStrongP@ssw0rd123'
    }, '/update-password');

    const response = await updatePasswordLambda(event as APIGatewayProxyEvent);
    expectErrorResponse(response, 401, 'INVALID_PARENT_PUBLIC_KEY', 'Invalid parent public key');
  });
});

describe('Update Password Counter Tests', () => {
  beforeAll(async () => {
    process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
    process.env.ENCRYPTION_KEY = "dBG9kF7bH5cJ2mN8pQ4sT6wX0yZ3vA1eI/nL+uM9oR4=";
    await initializeTestDatabase();
  });

  afterAll(async () => {
    delete process.env.USER_DETAILS_TABLE_NAME;
    delete process.env.ENCRYPTION_KEY;
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    jest.spyOn(require('../../shared/auth/passwordUtils'), 'hashPassword')
      .mockResolvedValue('mocked-hashed-password');
  });

  it.skip('should increment counter on successful password update', async () => {
    const testUser = await createTestUser({
      emailVerified: true,
      resetPasswordOTP: '654321',
      resetPasswordOTPExpiry: Date.now() + 3600000,
      accountType: 'parent',
      parentAccount: 'ROOT'
    });
    await seedTestUser(testUser);

    const mockDecodedToken = {
      email: testUser.email,
      otp: '654321'
    };

    jest.spyOn(require('../../shared/utils/tokenUtils'), 'decryptResetToken')
      .mockReturnValue(mockDecodedToken);

    const event = createMockEvent({
      token: 'valid-token',
      password: 'NewStrongP@ssw0rd123'
    }, '/update-password');

    await updatePasswordLambda(event as APIGatewayProxyEvent);
    
    const updatedUser = await getTestUser(testUser.userID);
    expect(updatedUser?.passwordUpdateCount).toBe(1);
  });
});
