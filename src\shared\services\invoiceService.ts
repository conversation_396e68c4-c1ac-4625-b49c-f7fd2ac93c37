import PDFDocument from 'pdfkit';
import { InvoiceData, PDFOptions } from '../types/invoice';
import path from 'path';

export class InvoiceService {
    private static defaultPDFOptions: PDFOptions = {
        format: 'a4',
        margin: {
            top: '20px',
            right: '20px',
            bottom: '20px',
            left: '20px'
        }
    };

    static async generatePDF(data: InvoiceData): Promise<Buffer> {
        console.log('Starting PDF generation for invoice:', data?.invoiceNumber);

        try {
            if (!data || !data.amount) {
                throw new Error('Failed to generate invoice PDF: Invalid invoice data provided');
            }

            // Create a new PDF document
            const doc = new PDFDocument({
                size: 'A4',
                margin: 50
            });

            // Create a buffer to store the PDF
            const chunks: Buffer[] = [];
            doc.on('data', (chunk) => chunks.push(chunk));

            // Add content
            await this.addInvoiceContent(doc, data);

            // Important: End the document!
            doc.end();

            // Return promise that resolves with the complete PDF data
            return new Promise((resolve, reject) => {
                doc.on('end', () => {
                    const pdfBuffer = Buffer.concat(chunks);
                    console.log('PDF generated for invoice:', data.invoiceNumber);
                    resolve(pdfBuffer);
                });
                doc.on('error', reject);
            });

        } catch (error) {
            console.error('Error generating PDF:', error);
            throw error;
        }
    }

    private static async addInvoiceContent(doc: PDFKit.PDFDocument, data: InvoiceData): Promise<void> {
        // Set default font family and add background
        doc.font('Helvetica')
           .rect(0, 0, 595.28, 841.89)
           .fill('#ffffff');
    
        // Add logo with better spacing
        try {
            // Use the local PNG file instead of fetching from URL
            const logoPath = path.join(__dirname, '../../../src/logo/Auth-logo.png');
            doc.image(logoPath, 60, 60, { width: 80 });
        } catch (error) {
            console.warn('Could not load logo image:', error);
            // Add fallback text if logo fails to load
            doc.font('Helvetica-Bold')
               .fontSize(14)
               .fillColor('#000000')
               .text('Authiqa', 60, 60, { width: 80, align: 'left' });
        }

        // Header section with adjusted positioning - removed the duplicate "Authiqa" text
        doc.fontSize(48)
           .fillColor('#9E9E9E')
           .text('Invoice', 350, 50, { align: 'right' });

        // Billed To section with smaller font
        doc.font('Helvetica-Bold')
           .fontSize(10)              // Changed from 12 to 10
           .fillColor('#000000')
           .text('BILLED TO:', 60, 150)
           .font('Helvetica')
           .fontSize(12)
           .text(data.organizationName || 'Customer', 60, 170);

        // Invoice details on right
        doc.font('Helvetica')
           .fontSize(12)
           .text(`Invoice No. ${data.invoiceNumber}`, 350, 150, { align: 'right' })
           .text(new Date(data.date).toLocaleDateString('en-US', {
               year: 'numeric',
               month: 'long',
               day: 'numeric'
           }), 350, 170, { align: 'right' });

        // Adjust right margin and spacing
        const rightMargin = 535;      // Changed from 470 to 535

        // First line above headers
        doc.moveTo(60, 250)
           .lineTo(rightMargin, 250)
           .stroke();

        // Table headers with bold font and adjusted position
        doc.font('Helvetica-Bold')
           .fontSize(12)
           .text('Item', 60, 260)
           .text('Amount', rightMargin - 60, 260, { align: 'right' });

        // Second line below headers
        doc.moveTo(60, 280)
           .lineTo(rightMargin, 280)
           .stroke();

        // Payment details with adjusted position
        doc.font('Helvetica')
           .fontSize(12)
           .text('Top-Up', 60, 290)
           .text(`$${(data.amount).toFixed(2)}`, rightMargin - 60, 290, { align: 'right' });

        // Third line below Top-Up
        doc.moveTo(60, 310)
           .lineTo(rightMargin, 310)
           .stroke();

        // Totals section with adjusted positioning
        doc.font('Helvetica-Bold')
           .text('Subtotal', rightMargin - 150, 330)
           .font('Helvetica')
           .text(`$${(data.amount).toFixed(2)}`, rightMargin - 60, 330, { align: 'right' })
           .font('Helvetica-Bold')
           .text('Tax', rightMargin - 150, 350)
           .font('Helvetica')
           .text('$0.00', rightMargin - 60, 350, { align: 'right' })
           .font('Helvetica-Bold')
           .text('Total', rightMargin - 150, 380)
           .font('Helvetica-Bold')
           .text(`$${(data.amount).toFixed(2)}`, rightMargin - 60, 380, { align: 'right' });
    }

    static async generateInvoiceNumber(transactionId: string): Promise<string> {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const shortId = transactionId.slice(-4);
        return `INV-${year}${month}${day}-${shortId}`;
    }
}
