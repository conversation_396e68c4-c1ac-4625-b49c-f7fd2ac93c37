import { AuthUrls } from '../types';

export function generateDefaultAuthUrls(organizationUrl: string): AuthUrls {
    // Remove trailing slash if present
    const baseUrl = organizationUrl.replace(/\/+$/, '');

    return {
        signup: `${baseUrl}/signup.html`,
        signin: `${baseUrl}/signin.html`,
        verify: `${baseUrl}/verify.html`,
        reset: `${baseUrl}/reset.html`,
        update: `${baseUrl}/update.html`,
        resend: `${baseUrl}/resend.html`,
        successful: `${baseUrl}/successful.html`
    };
}
