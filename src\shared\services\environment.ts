import { getSmtpPassword } from '../secrets';
import { getConfig } from './configService';
import { APIGatewayProxyEvent } from 'aws-lambda';

export const Environment = {
  isLocal: () => process.env.NODE_ENV === 'local' || process.env.IS_LOCAL === 'true',
  isProduction: () => process.env.NODE_ENV === 'production',
  
  getBaseUrl: async (event?: APIGatewayProxyEvent) => {
    // If we have an event, use the new config service
    if (event) {
      try {
        const config = await getConfig(event);
        return config.FRONTEND_URL;
      } catch (error) {
        console.error('Error getting FRONTEND_URL from config:', error);
      }
    }
    
    // Fallback to environment variable
    console.log('Environment state:', {
      NODE_ENV: process.env.NODE_ENV,
      FRONTEND_URL: process.env.FRONTEND_URL,
      isProduction: Environment.isProduction(),
      isLocal: Environment.isLocal()
    });

    // Always use FRONTEND_URL from environment
    const frontendUrl = process.env.FRONTEND_URL;
    if (!frontendUrl) {
      throw new Error('FRONTEND_URL environment variable is required');
    }

    console.log(`Using frontend URL: ${frontendUrl}`);
    return frontendUrl;
  },

  getEmailConfig: async (event?: APIGatewayProxyEvent) => {
    // If we have an event, use the new config service
    if (event) {
      try {
        const config = await getConfig(event);
        return {
          host: config.SMTP_HOST,
          port: parseInt(config.SMTP_PORT || '465'),
          user: config.SMTP_USER,
          password: await getSmtpPassword(event),
          secure: true,
          skipEmailVerification: Environment.isLocal()
        };
      } catch (error) {
        console.error('Error getting email config from config service:', error);
      }
    }
    
    // Fallback to environment variables
    return {
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '465'),
      user: process.env.SMTP_USER,
      password: await getSmtpPassword(),
      secure: true,
      skipEmailVerification: Environment.isLocal()
    };
  },

  getAWSConfig: async (event?: APIGatewayProxyEvent) => {
    // If we have an event, use the new config service
    if (event) {
      try {
        const config = await getConfig(event);
        return {
          region: config.REGION || 'eu-west-1',
          credentials: {
            accessKeyId: config.AWS_ACCESS_KEY_ID!,
            secretAccessKey: config.AWS_SECRET_ACCESS_KEY!
          }
        };
      } catch (error) {
        console.error('Error getting AWS config from config service:', error);
      }
    }
    
    // Fallback to environment variables
    return {
      region: process.env.AWS_REGION || 'eu-west-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!
      }
    };
  },

  getTestConfig: async (event?: APIGatewayProxyEvent) => {
    // If we have an event, use the new config service
    if (event) {
      try {
        const config = await getConfig(event);
        return {
          FRONTEND_URL: config.FRONTEND_URL,
          USER_DETAILS_TABLE_NAME: config.USER_DETAILS_TABLE_NAME,
          JWT_SECRET: config.JWT_SECRET
        };
      } catch (error) {
        console.error('Error getting test config from config service:', error);
      }
    }
    
    // Fallback to environment variables
    return {
      FRONTEND_URL: process.env.FRONTEND_URL || 'https://authiqa.com',
      USER_DETAILS_TABLE_NAME: process.env.USER_DETAILS_TABLE_NAME || 'test-users',
      JWT_SECRET: process.env.JWT_SECRET || 'test-secret'
    };
  },
  
  // New methods for Telegram credentials
  getTelegramBotToken: async (event?: APIGatewayProxyEvent) => {
    if (event) {
      try {
        const config = await getConfig(event);
        return config.TELEGRAM_BOT_TOKEN;
      } catch (error) {
        console.error('Error getting Telegram bot token from config service:', error);
      }
    }
    return process.env.TELEGRAM_BOT_TOKEN;
  },
  
  getTelegramChatId: async (event?: APIGatewayProxyEvent) => {
    if (event) {
      try {
        const config = await getConfig(event);
        return config.TELEGRAM_CHAT_ID;
      } catch (error) {
        console.error('Error getting Telegram chat ID from config service:', error);
      }
    }
    return process.env.TELEGRAM_CHAT_ID;
  }
}; 

export const validateEmailConfig = async (event?: APIGatewayProxyEvent) => {
  let requiredVars: string[];
  let missingVars: string[] = [];
  
  if (event) {
    try {
      const config = await getConfig(event);
      requiredVars = [
        'SMTP_HOST',
        'SMTP_PORT',
        'SMTP_USER',
        'FRONTEND_URL'
      ];
      
      missingVars = requiredVars.filter(varName => !config[varName]);
    } catch (error) {
      console.error('Error validating email config from config service:', error);
      // Fall back to environment variables
      requiredVars = [
        'SMTP_HOST',
        'SMTP_PORT',
        'SMTP_USER',
        'SMTP_PASSWORD',
        'FRONTEND_URL'
      ];
      
      missingVars = requiredVars.filter(varName => !process.env[varName]);
    }
  } else {
    requiredVars = [
      'SMTP_HOST',
      'SMTP_PORT',
      'SMTP_USER',
      'SMTP_PASSWORD',
      'FRONTEND_URL'
    ];
    
    missingVars = requiredVars.filter(varName => !process.env[varName]);
  }
  
  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }
}; 
