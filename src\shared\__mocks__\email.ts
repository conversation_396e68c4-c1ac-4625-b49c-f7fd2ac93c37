export const sendVerificationEmail = jest.fn().mockImplementation(
  async (email: string, token: string, accountType?: string, parentPublicKey?: string) => {
    const verificationLink = `${process.env.FRONTEND_URL}/confirm-email?token=${token}`;
    console.log('Mock: Sending verification email to', email, 'with verification link:', verificationLink);
    console.log('Account Type:', accountType, 'Parent Public Key:', parentPublicKey);
    return Promise.resolve();
  }
);

export const sendConfirmationEmail = sendVerificationEmail;

export default {
  sendVerificationEmail,
  sendConfirmationEmail
}; 