import { DynamoDB } from 'aws-sdk'; 

export const PaymentHistoryTable = {
  TableName: 'paymentHistory',
  AttributeDefinitions: [
    { AttributeName: 'paymentId', AttributeType: 'S' },
    { AttributeName: 'userID', AttributeType: 'S' },
    { AttributeName: 'publicKey', AttributeType: 'S' },
    { AttributeName: 'createdAt', AttributeType: 'N' },
    { AttributeName: 'status', AttributeType: 'S' },
    { AttributeName: 'stripePaymentIntentId', AttributeType: 'S' }  // New attribute
  ],
  KeySchema: [
    { AttributeName: 'paymentId', KeyType: 'HASH' }
  ],
  GlobalSecondaryIndexes: [
    {
      IndexName: 'userID-createdAt-index',
      KeySchema: [
        { AttributeName: 'userID', KeyType: 'HASH' },
        { AttributeName: 'createdAt', KeyType: 'RANGE' }
      ],
      Projection: { ProjectionType: 'ALL' },
     
    },
    {
      IndexName: 'publicKey-createdAt-index',
      KeySchema: [
        { AttributeName: 'publicKey', KeyType: 'HASH' },
        { AttributeName: 'createdAt', KeyType: 'RANGE' }
      ],
      Projection: { ProjectionType: 'ALL' },
   
    },
    {
      IndexName: 'status-createdAt-index',
      KeySchema: [
        { AttributeName: 'status', KeyType: 'HASH' },
        { AttributeName: 'createdAt', KeyType: 'RANGE' }
      ],
      Projection: { ProjectionType: 'ALL' },
    
    },
    {
      IndexName: 'stripePaymentIntent-index',
      KeySchema: [
        { AttributeName: 'stripePaymentIntentId', KeyType: 'HASH' }
      ],
      Projection: { ProjectionType: 'ALL' },
     
    }
  ],

  BillingMode: 'PAY_PER_REQUEST'  // Changed to On-Demand
};




