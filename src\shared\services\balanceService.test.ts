// Mock setup must be before imports
const mockStripeInstance = {
  paymentIntents: {
    create: jest.fn(),
    retrieve: jest.fn()
  }
};

jest.mock('stripe', () => {
  return jest.fn(() => mockStripeInstance);
});

// Now we can have our imports
import {  validatePaymentRequest, initializePayment, finalizePayment, handleFailedPayment } from './balanceService';
import { createPaymentRecord, updatePaymentStatus, getPaymentById } from '../database/paymentOperations';
import { sendPaymentConfirmationEmail } from '../emailService';
import { PaymentStatus, PaymentHistory } from '../types/payment';
import { createTestUser } from '../testUtils/lambdaTestUtils';
import { initializeTestDatabase, seedTestUser } from '../testUtils/databaseLifecycle';
import { DynamoDB } from 'aws-sdk';
import Stripe from 'stripe';
import { getUserById } from '../database/userOperations';
import { getTestUser } from '../testUtils/databaseTestUtils';
import { User } from '../types';
import { PaymentUser } from './balanceService';

// Helper function to convert User to PaymentUser
const toPaymentUser = (user: User) => ({
  userID: user.userID,
  publicKey: user.publicKey,
  email: user.email,
  username: user.username,
  balance: user.accountBalance || 0,
  accountType: user.accountType
});

// Only mock external services
jest.mock('../emailService');

const dynamoDB = new DynamoDB.DocumentClient();

describe('Balance Service', () => {
  beforeAll(async () => {
    process.env.PAYMENT_HISTORY_TABLE_NAME = 'paymentHistory';
    process.env.USER_DETAILS_TABLE_NAME = 'userAuthentication';
    process.env.STRIPE_SECRET_KEY = 'sk_test_51Qvxaz2LMeGvIu8sDTORi0bE4Ybo6GDEWXvHLp2en3X5O0tIKQujv6Rfzxb8B93m6NIMB3vbRu0OGp7RzrGC9WrK00WloK0zrg';
    await initializeTestDatabase();
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockStripeInstance.paymentIntents.create.mockReset();
    mockStripeInstance.paymentIntents.retrieve.mockReset();
  });

  describe('initializePayment', () => {
    it.skip('should initialize payment successfully', async () => {
      // Create and seed real test user
      const testUser = await createTestUser({
        accountBalance: 100,
        accountType: 'parent'
      });
      await seedTestUser(testUser);

      const amount = 50;
      const mockPaymentIntent = {
        client_secret: 'test_secret',
        id: 'test_intent_id'
      };
      
      mockStripeInstance.paymentIntents.create.mockResolvedValue(mockPaymentIntent);
      
      const result = await initializePayment(toPaymentUser(testUser), amount);
      
      // Verify real database state
      const paymentRecord = await getPaymentById(result.paymentId);
      expect(paymentRecord).toBeDefined();
      expect(paymentRecord?.status).toBe(PaymentStatus.PENDING);
      expect(paymentRecord?.amount).toBe(amount);
    });

    it.skip('should handle initialization failure', async () => {
      const testUser = await createTestUser({
        accountBalance: 100,
        accountType: 'parent'
      });
      await seedTestUser(testUser);

      // Force a failure by passing invalid amount
      await expect(initializePayment(toPaymentUser(testUser), -50))
        .rejects.toThrow('Payment initialization failed');
    });
  });

  describe('finalizePayment', () => {
    it.skip('should finalize payment successfully', async () => {
      const testUser = await createTestUser({
        accountBalance: 100,
        accountType: 'parent'
      });
      await seedTestUser(testUser);

      // First initialize a payment
      const amount = 50;
      const mockPaymentIntent = {
        client_secret: 'test_secret',
        id: 'test_intent_id'
      };
      
      mockStripeInstance.paymentIntents.create.mockResolvedValue(mockPaymentIntent);
      const initializedPayment = await initializePayment(toPaymentUser(testUser), amount);
      
      mockStripeInstance.paymentIntents.retrieve.mockResolvedValue({
        id: mockPaymentIntent.id,
        status: 'succeeded'
      });
      
      const result = await finalizePayment(
        initializedPayment.paymentId, 
        mockPaymentIntent.id,
        process.env.PAYMENT_HISTORY_TABLE_NAME || 'paymentHistory',
        process.env.USER_DETAILS_TABLE_NAME || 'userAuthentication'
      );
      
      // Verify real database state
      const paymentRecord = await getPaymentById(initializedPayment.paymentId);
      expect(paymentRecord?.status).toBe(PaymentStatus.COMPLETED);
      
      const updatedUser = await getTestUser(testUser.userID);
      if (!updatedUser) {
        throw new Error('Updated user not found');
      }
      expect(updatedUser.accountBalance).toBe(150); // 100 + 50
    });

    it.skip('should handle payment not found', async () => {
      await expect(finalizePayment(
        'non-existent', 
        'test_intent_id',
        process.env.PAYMENT_HISTORY_TABLE_NAME || 'paymentHistory',
        process.env.USER_DETAILS_TABLE_NAME || 'userAuthentication'
      ))
        .rejects.toThrow('Payment record not found');
    });

    it.skip('should handle multiple payments from same user successfully', async () => {
      // Create test user with initial balance of 100
      const testUser = await createTestUser({
        accountBalance: 100,
        accountType: 'parent'
      });
      await seedTestUser(testUser);

      // Define multiple payments
      const payments = [
        { amount: 50, intentId: 'test_intent_1' },
        { amount: 75, intentId: 'test_intent_2' },
        { amount: 25, intentId: 'test_intent_3' },
        { amount: 100, intentId: 'test_intent_4' },
        { amount: 150, intentId: 'test_intent_5' }
      ];

      let currentBalance = 100; // Initial balance

      // Process each payment sequentially
      for (const payment of payments) {
        // Mock Stripe payment intent creation
        const mockPaymentIntent = {
          client_secret: 'test_secret',
          id: payment.intentId
        };
        mockStripeInstance.paymentIntents.create.mockResolvedValueOnce(mockPaymentIntent);

        // Initialize payment
        const initializedPayment = await initializePayment(toPaymentUser(testUser), payment.amount);

        // Mock Stripe payment intent retrieval
        mockStripeInstance.paymentIntents.retrieve.mockResolvedValueOnce({
          id: payment.intentId,
          status: 'succeeded'
        });

        // Finalize payment
        await finalizePayment(
          initializedPayment.paymentId, 
          payment.intentId,
          process.env.PAYMENT_HISTORY_TABLE_NAME || 'paymentHistory',
          process.env.USER_DETAILS_TABLE_NAME || 'userAuthentication'
        );

        // Update expected balance
        currentBalance += payment.amount;

        // Verify payment record status
        const paymentRecord = await getPaymentById(initializedPayment.paymentId);
        expect(paymentRecord?.status).toBe(PaymentStatus.COMPLETED);

        // Verify user's balance after each payment
        const updatedUser = await getTestUser(testUser.userID);
        if (!updatedUser) {
          throw new Error('Updated user not found');
        }
        expect(updatedUser.accountBalance).toBe(currentBalance);
      }

      // Final verification
      const finalUser = await getTestUser(testUser.userID);
      if (!finalUser) {
        throw new Error('Final user not found');
      }
      // Should be 100 + 50 + 75 + 25 + 100 + 150 = 500
      expect(finalUser.accountBalance).toBe(500);
    });
  });

  describe('handleFailedPayment', () => {
    it.skip('should handle failed payment correctly', async () => {
      const testUser = await createTestUser({
        accountBalance: 100,
        accountType: 'parent'
      });
      await seedTestUser(testUser);

      // First initialize a payment
      const amount = 50;
      const mockPaymentIntent = {
        client_secret: 'test_secret',
        id: 'test_intent_id'
      };
      
      mockStripeInstance.paymentIntents.create.mockResolvedValue(mockPaymentIntent);
      const initializedPayment = await initializePayment(toPaymentUser(testUser), amount);
      
      await handleFailedPayment(
        initializedPayment.paymentId, 
        mockPaymentIntent.id,
        process.env.PAYMENT_HISTORY_TABLE_NAME || 'paymentHistory',
    process.env.USER_DETAILS_TABLE_NAME || 'userAuthentication'
      );
      
      // Verify real database state
      const paymentRecord = await getPaymentById(initializedPayment.paymentId);
      expect(paymentRecord?.status).toBe(PaymentStatus.FAILED);
      
      const updatedUser = await getTestUser(testUser.userID);
      if (!updatedUser) {
        throw new Error('Updated user not found');
      }
      expect(updatedUser.accountBalance).toBe(100); // Balance unchanged
    });
  });

  describe('validatePaymentRequest', () => {
    it.skip('should validate valid payment request', async () => {
      const testUser = await createTestUser({
        accountBalance: 100,
        accountType: 'parent'
      });
      await seedTestUser(testUser);

      expect(() => validatePaymentRequest(toPaymentUser(testUser), 50)).not.toThrow();
    });

    // Rest of validation tests remain the same as they don't need database operations
    it.skip('should reject invalid user data', () => {
      const invalidUser = {
        userID: '',
        publicKey: '',
        email: '',
        username: '',
        balance: 0,
        accountType: 'parent'
      };
      expect(() => validatePaymentRequest(invalidUser, 50))
        .toThrow('Invalid user data');
    });

    it.skip('should reject invalid amount', () => {
      const testUser = {
        userID: 'test',
        publicKey: 'test-key',
        email: '<EMAIL>',
        username: 'test',
        balance: 100,
        accountType: 'parent'
      };
      expect(() => validatePaymentRequest(testUser, 0))
        .toThrow('Invalid payment amount');
      expect(() => validatePaymentRequest(testUser, -50))
        .toThrow('Invalid payment amount');
    });

    it.skip('should reject non-parent accounts', () => {
      const childUser = {
        userID: 'test',
        publicKey: 'test-key',
        email: '<EMAIL>',
        username: 'test',
        balance: 100,
        accountType: 'child'
      };
      expect(() => validatePaymentRequest(childUser, 50))
        .toThrow('Only parent accounts can make payments');
    });
  });
});