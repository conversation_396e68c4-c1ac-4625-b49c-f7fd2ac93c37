import { DynamoDB } from 'aws-sdk';
import { PaymentHistory, PaymentStatus } from '../types/payment';
import { v4 as uuidv4 } from 'uuid';

const dynamoDB = new DynamoDB.DocumentClient();
const TABLE_NAME = process.env.PAYMENT_HISTORY_TABLE_NAME || 'paymentHistory';

// Create a new payment record when payment is initiated
export async function createPaymentRecord(
  userID: string,
  publicKey: string,
  amount: number,
  previousBalance: number,
  transactionId: string,
  stripePaymentIntentId?: string,
  invoiceEmail?: string,
  tableName?: string  // Add tableName parameter
): Promise<PaymentHistory> {
  if (!userID || !publicKey || amount <= 0) {
    throw new Error('Invalid payment record parameters');
  }

  const payment: PaymentHistory = {
    paymentId: uuidv4(),
    userID,
    publicKey,
    amount,
    status: PaymentStatus.PENDING,
    createdAt: Date.now(),
    previousBalance,
    newBalance: previousBalance,
    transactionId,
    stripePaymentIntentId,
    invoiceEmail
  };

  try {
    await dynamoDB.put({
      TableName: tableName || TABLE_NAME,  // Use provided tableName or default
      Item: payment
    }).promise();
    
    console.log('Created payment record:', payment);
    return payment;
  } catch (error) {
    console.error('Error creating payment record:', error);
    throw new Error(`Failed to create payment record: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
// Add new function after existing functions
export async function getPaymentByStripeIntentId(
  stripePaymentIntentId: string,
  tableName?: string
): Promise<PaymentHistory | null> {
  try {
    const params = {
      TableName: tableName || TABLE_NAME,
      IndexName: 'stripePaymentIntent-index',
      KeyConditionExpression: 'stripePaymentIntentId = :stripeId',
      ExpressionAttributeValues: {
        ':stripeId': stripePaymentIntentId
      }
    };
    
    const result = await dynamoDB.query(params).promise();
    return result.Items?.[0] as PaymentHistory || null;
  } catch (error) {
    console.error('Error getting payment by Stripe intent ID:', error);
    throw error;
  }
}

// Modify existing updatePaymentStatus function
export async function updatePaymentStatus(
  paymentId: string,
  status: PaymentStatus,
  newBalance?: number,
  stripePaymentIntentId?: string,
  tableName?: string  // Add tableName parameter
): Promise<PaymentHistory> {
  try {
    // First, get the existing payment record
    const existingPayment = await getPaymentById(paymentId, tableName);
    if (!existingPayment) {
      throw new Error('Payment record not found');
    }

    const updateExpressions: string[] = ['#status = :status', 'completedAt = :completedAt'];
    const expressionAttributeValues: any = {
      ':status': status,
      ':completedAt': Date.now()
    };

    if (newBalance !== undefined) {
      updateExpressions.push('newBalance = :newBalance');
      expressionAttributeValues[':newBalance'] = newBalance;
    }

    if (stripePaymentIntentId) {
      updateExpressions.push('stripePaymentIntentId = :stripeId');
      expressionAttributeValues[':stripeId'] = stripePaymentIntentId;
    }

    try {
      await dynamoDB.update({
        TableName: tableName || TABLE_NAME,  // Use provided tableName or default
        Key: { paymentId },
        UpdateExpression: `SET ${updateExpressions.join(', ')}`,
        ExpressionAttributeNames: {
          '#status': 'status'
        },
        ExpressionAttributeValues: expressionAttributeValues
      }).promise();
      
      console.log(`Updated payment ${paymentId} status to ${status}`);
    } catch (error) {
      console.error(`Error updating payment status:`, error);
      throw error;
    }
    // Return the complete PaymentHistory object
    return {
      paymentId,
      status,
      amount: existingPayment.amount,
      previousBalance: existingPayment.previousBalance,
      newBalance: newBalance || existingPayment.previousBalance,
      userID: existingPayment.userID,
      publicKey: existingPayment.publicKey,
      transactionId: existingPayment.transactionId,
      createdAt: existingPayment.createdAt,
      completedAt: status === PaymentStatus.COMPLETED ? Date.now() : undefined,
      stripePaymentIntentId
    };
  } catch (error) {
    console.error(`Error updating payment status:`, error);
    throw error;
  }
}

// Get payment history for a user
export async function getUserPaymentHistory(
  userID: string,
  limit: number = 10,
  startKey?: any,
  tableName?: string  // Add tableName parameter
): Promise<{
  payments: PaymentHistory[];
  lastEvaluatedKey?: any;
}> {
  try {
    const params: DynamoDB.DocumentClient.QueryInput = {
      TableName: tableName || TABLE_NAME,  // Use provided tableName or default
      IndexName: 'userID-createdAt-index',
      KeyConditionExpression: 'userID = :userID',
      ExpressionAttributeValues: {
        ':userID': userID
      },
      ScanIndexForward: false, // Most recent first
      Limit: limit
    };

    if (startKey) {
      params.ExclusiveStartKey = startKey; // Use the full key object
    }

    const result = await dynamoDB.query(params).promise();
    
    return {
      payments: result.Items as PaymentHistory[],
      lastEvaluatedKey: result.LastEvaluatedKey // Return the full key object
    };
  } catch (error) {
    console.error('Error getting payment history:', error);
    throw error;
  }
}

// Get payment history by publicKey with date range support
export async function getPaymentHistoryBypublicKey(
  publicKey: string,
  startDate?: number,
  endDate?: number,
  limit: number = 10,
  startKey?: any,
  tableName?: string  // Add tableName parameter
): Promise<{
  payments: PaymentHistory[];
  lastEvaluatedKey?: any;
}> {
  try {
    const params: DynamoDB.DocumentClient.QueryInput = {
      TableName: tableName || TABLE_NAME,  // Use provided tableName or default
      IndexName: 'publicKey-createdAt-index',
      KeyConditionExpression: 'publicKey = :publicKey',
      ExpressionAttributeValues: {
        ':publicKey': publicKey
      },
      ScanIndexForward: false,
      Limit: limit
    };

    if (startDate && endDate) {
      params.KeyConditionExpression += ' AND createdAt BETWEEN :startDate AND :endDate';
      params.ExpressionAttributeValues = {
        ...params.ExpressionAttributeValues,
        ':startDate': startDate,
        ':endDate': endDate
      };
    }

    if (startKey) {
      params.ExclusiveStartKey = startKey;
    }

    const result = await dynamoDB.query(params).promise();
    
    return {
      payments: result.Items as PaymentHistory[],
      lastEvaluatedKey: result.LastEvaluatedKey
    };
  } catch (error) {
    console.error('Error getting payment history by publicKey:', error);
    throw new Error(`Failed to get payment history: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function getPaymentById(
  paymentId: string,
  tableName?: string  // Add tableName parameter
): Promise<PaymentHistory | null> {
  console.log('Fetching payment with ID:', paymentId);
  try {
    const result = await dynamoDB.get({
      TableName: tableName || TABLE_NAME,  // Use provided tableName or default
      Key: { paymentId },
      ConsistentRead: true
    }).promise();
    
    console.log('Payment record found:', result.Item);
    return result.Item as PaymentHistory || null;
  } catch (error) {
    console.error('Error getting payment by ID:', error);
    throw error;
  }
}

export async function getPaymentsByStatus(
  status: PaymentStatus,
  limit: number = 10,
  tableName?: string  // Add tableName parameter
): Promise<PaymentHistory[]> {
  try {
    const params: DynamoDB.DocumentClient.QueryInput = {
      TableName: tableName || TABLE_NAME,  // Use provided tableName or default
      IndexName: 'status-createdAt-index',
      KeyConditionExpression: '#status = :status',
      ExpressionAttributeNames: {
        '#status': 'status'
      },
      ExpressionAttributeValues: {
        ':status': status
      },
      Limit: limit,
      ScanIndexForward: false
    };

    const result = await dynamoDB.query(params).promise();
    return result.Items as PaymentHistory[];
  } catch (error) {
    console.error('Error getting payments by status:', error);
    throw error;
  }
}
