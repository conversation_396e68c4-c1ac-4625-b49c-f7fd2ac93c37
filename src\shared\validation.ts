import * as Yup from 'yup';

// Password validation
const specialChars = '!@#$%^&*()_+-=[]{};\'",./<>?\\|';
const escapedSpecialChars = specialChars.split('').map(char => '\\' + char).join('');

const passwordStrengthRegex = new RegExp(
  `^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[${escapedSpecialChars}])[A-Za-z\\d${escapedSpecialChars}]{8,}$`
);

export const passwordSchema = Yup.string()
  .required('No password was provided. Please include a password in your request')
  .min(8, 'The password must be at least 8 characters long')
  .matches(
    passwordStrengthRegex, 
    'The provided password does not meet the required strength criteria. Please ensure your password is at least 8 characters long, includes both uppercase and lowercase letters, at least one numeric digit, and one special character.'
  )
  .test('password-complexity', 'The password is not complex enough. Please use a mix of characters, numbers, and symbols.', (value) => {
    if (!value) return false;
    const uniqueChars = new Set(value).size;
    return uniqueChars >= 4; // Already set to 4 unique characters
  });

// Email validation
export const emailSchema = Yup.string()
  .email('The email address is not in a valid format. Please provide a valid email.')
  .required('No email address was provided. Please include an email address in your request');

// Username validation
export const usernameSchema = Yup.string()
  .required('No username was provided. Please include a username in your request')
  .matches(/^[a-zA-Z0-9_]+$/, 'The username contains unsupported characters. Please use only alphanumeric characters and underscores')
  .min(3, 'Username must be at least 3 characters long')
  .max(30, 'Username must not exceed 30 characters');

// Common schema compositions
export const signInSchema = Yup.object().shape({
  email: emailSchema,
  password: passwordSchema
});

export const signUpSchema = Yup.object().shape({
  username: usernameSchema,
  email: emailSchema,
  password: passwordSchema
});

export const resendConfirmationEmailSchema = Yup.object().shape({
  email: emailSchema
});

// Add this to the existing shared validation file
export const OTPSchema = Yup.string()
  .required('No OTP was provided. Please include an OTP in your request')
  .matches(/^\d{6}$/, 'The OTP must be a 6-digit number');

export const emailConfirmationSchema = Yup.object().shape({
  OTP: OTPSchema
});

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};


export const validateUsername = (username: string): boolean => {
  // Only letters, numbers, and underscores allowed
  const usernameRegex = /^[a-zA-Z0-9_]+$/;
  return usernameRegex.test(username);
};
