import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDB } from 'aws-sdk';

export const createMockEvent = (body: any, path: string = '/'): Partial<APIGatewayProxyEvent> => ({
  body: JSON.stringify(body),
  headers: {},
  multiValueHeaders: {},
  httpMethod: 'POST',
  isBase64Encoded: false,
  path,
  pathParameters: null,
  queryStringParameters: null,
  multiValueQueryStringParameters: null,
  stageVariables: null,
  requestContext: {
    identity: {
      sourceIp: '127.0.0.1',
    },
  } as any,
  resource: '',
});

export const createMockContext = (): Partial<Context> => ({
  callbackWaitsForEmptyEventLoop: true,
  functionName: 'testFunction',
  functionVersion: '1',
  invokedFunctionArn: 'arn:aws:lambda:us-east-1:123456789012:function:testFunction',
  memoryLimitInMB: '128',
  awsRequestId: '0123456789',
  logGroupName: '/aws/lambda/testFunction',
  logStreamName: '2021/01/01/[$LATEST]0123456789abcdef',
});

export const createMockDynamoDB = (mockImplementation: any) => {
  return {
    get: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue(mockImplementation.get),
    }),
    query: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue(mockImplementation.query),
    }),
    update: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue(mockImplementation.update),
    }),
    put: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue(mockImplementation.put),
    }),
    transactWrite: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue(mockImplementation.transactWrite),
    }),
  };
};
